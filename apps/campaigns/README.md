This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

App Structure & Flow
1.Main Page
The entry point is page.tsx, which renders two main components side by side:

CampaignForm: The input form for campaign details.
ContentPreview: Shows generated campaign content.
State Management
State is managed globally using zustand via the useAppStore hook, defined in store.ts.

The store holds:
businessName, businessDescription, visualStyle: Form fields.
campaignData: The generated campaign content.
loading: Boolean for async state.
error: Error message if any.
Setter functions for each field.
Form Submission

When the user submits the form in CampaignForm, it sends a POST request to /api/generate-campaign.
The form uses the setter functions from useAppStore to update state (setLoading, setError, setCampaignData).
API Route

The API endpoint is implemented in route.ts.
It receives the form data, generates campaign content, and returns it as JSON.
Content Preview

ContentPreview reads campaignData, loading, and error from the store.
It conditionally renders a loading spinner, error message, or the generated campaign cards (PostCard, CarouselCard, StoryCard).
State Management Details
Global Store:
useAppStore is a Zustand store that provides global state and setters.
Any component can read or update state by calling this hook.

Form Interaction:

Form fields are bound to store values and update via setters.
On submit, the store is updated to show loading, clear errors, and reset previous campaign data.
Async Flow:

While waiting for the API, loading is set to true.
On success, campaignData is updated.
On error, error is set.
Preview Rendering:

The preview component reacts to changes in the store and updates the UI accordingly.
Key Files
store.ts: Zustand store definition.
CampaignForm.tsx: Form UI and submission logic.
ContentPreview.tsx: Preview UI, reads from store.
route.ts: API endpoint for campaign generation.
page.tsx: Main layout and component composition.
Summary
Your app uses Zustand for global state management. The form updates the store, triggers an API call, and the preview component reacts to store changes to show results. All state (form fields, loading, error, campaign data) is centralized in useAppStore.