{"name": "campaigns", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3006", "build": "next build", "start": "next start --port 3004", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@repo/auth": "workspace:^", "@repo/ui": "workspace:^", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "html2canvas": "^1.4.1", "lucide-react": "^0.533.0", "next": "^15.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.4.38", "tailwindcss": "^3.4.0", "tw-animate-css": "^1.3.5", "typescript": "^5"}}