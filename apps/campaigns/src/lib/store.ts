import { create } from 'zustand';

interface PostContent {
  content_type: string;
  post_type: string;
  main_text: string;
  subtext: string;
  cta_text: string;
  additional_text?: string;
  caption: string;
  hashtags: string[];
}

interface CampaignData {
  campaign_name: string;
  generated_content: PostContent[];
}

interface AppState {
  businessName: string;
  businessDescription: string;
  visualStyle: string;
  selectedPostType: string;
  campaignData: CampaignData | null;
  loading: boolean;
  error: string | null;
  setBusinessName: (name: string) => void;
  setBusinessDescription: (description: string) => void;
  setVisualStyle: (style: string) => void;
  setSelectedPostType: (type: string) => void;
  setCampaignData: (data: CampaignData | null) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (errorMessage: string | null) => void;
}

export const useAppStore = create<AppState>((set) => ({
  businessName: '',
  businessDescription: '',
  visualStyle: '',
  selectedPostType: 'Informative',
  campaignData: null,
  loading: false,
  error: null,
  setBusinessName: (name) => set({ businessName: name }),
  setBusinessDescription: (description) => set({ businessDescription: description }),
  setVisualStyle: (style) => set({ visualStyle: style }),
  setSelectedPostType: (type) => set({ selectedPostType: type }),
  setCampaignData: (data) => set({ campaignData: data }),
  setLoading: (isLoading) => set({ loading: isLoading }),
  setError: (errorMessage) => set({ error: errorMessage }),
}));
