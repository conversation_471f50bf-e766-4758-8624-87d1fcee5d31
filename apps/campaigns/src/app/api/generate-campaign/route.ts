import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    return NextResponse.json({ error: 'OpenAI API key not configured' }, { status: 500 });
  }

  try {
    const { businessName, businessDescription, visualStyle, selectedPostType } = await req.json();

    if (!businessName || !businessDescription || !visualStyle || !selectedPostType) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const prompt = `Generate detailed, engaging Instagram post content for:
Business: ${businessName}
Description: ${businessDescription}
Visual Style: ${visualStyle}
Post Type: ${selectedPostType}

Create 3 different variations of ${selectedPostType} posts with:

STRICT VISUAL POSITIONING RULES (MUST FOLLOW):
- Heading: x: 50, y: 20 (top area, always centered)
- Subheading: x: 50, y: 42 (middle area, minimum 20px gap from heading)
- Additional element: x: 50, y: 62 (lower middle, minimum 20px gap)
- CTA: x: 50, y: 85 (bottom area, minimum 20px gap)
- Font sizes: Heading (30-38px), Subheading (16-20px), Additional (18-24px), CTA (16-18px)
- Colors: High contrast for readability
- NO OVERLAPPING TEXT ALLOWED

CONTENT REQUIREMENTS:
- Main heading: Bold, catchy, 2-4 words maximum, Few emojis allowed (1-2)
- Subheading: Supporting details (6-10 words)
- Call-to-action: Action-oriented (2-3 words)
- Additional elements: Price, discount, or key benefit
- Rich captions: 2-3 sentences with emojis
- Strategic hashtags: 8-12 relevant tags

Ensure proper vertical spacing between all text elements.`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          { role: "system", content: "You are a social media content strategist. Generate detailed Instagram post content with precise visual positioning in JSON format." },
          { role: "user", content: prompt }
        ],
        functions: [{
          name: "generate_post_content",
          description: "Generate rich, engaging Instagram post content with detailed visual styling",
          parameters: {
            type: "object",
            properties: {
              campaign_name: { 
                type: "string",
                description: "Creative campaign name reflecting the business and style"
              },
              generated_content: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    content_type: { type: "string", enum: ["post"] },
                    post_type: { type: "string" },
                    main_text: { 
                      type: "string",
                      description: "Bold, attention-grabbing headline (3-6 words)"
                    },
                    subtext: { 
                      type: "string",
                      description: "Supporting details or emotional hook (8-15 words)"
                    },
                    cta_text: { 
                      type: "string",
                      description: "Action-oriented call-to-action (2-4 words)"
                    },
                    additional_text: { 
                      type: "string",
                      description: "Extra text like price, discount percentage, or location"
                    },
                    caption: { 
                      type: "string",
                      description: "Engaging 2-3 sentence caption with emojis and personality"
                    },
                    hashtags: { 
                      type: "array", 
                      items: { type: "string" },
                      description: "8-12 strategic hashtags mixing popular and niche tags"
                    },
                    visual_style_suggestion: {
                      type: "object",
                      properties: {
                        tag: { 
                          type: "string",
                          enum: ["informative", "product", "testimonial", "announcement", "offer"]
                        },
                        heading: {
                          type: "object",
                          properties: {
                            text: { type: "string" },
                            x: { type: "number", description: "X position as percentage (0-100)" },
                            y: { type: "number", description: "Y position as percentage (0-100)" },
                            font: {
                              type: "object",
                              properties: {
                                size: { type: "number", description: "Font size in pixels (24-48)" },
                                weight: { type: "string", enum: ["normal", "bold", "600", "700", "800"] },
                                color: { type: "string", description: "Hex color code" }
                              }
                            }
                          }
                        },
                        subheading: {
                          type: "object",
                          properties: {
                            text: { type: "string" },
                            x: { type: "number" },
                            y: { type: "number" },
                            font: {
                              type: "object",
                              properties: {
                                size: { type: "number", description: "Font size in pixels (16-24)" },
                                weight: { type: "string" },
                                color: { type: "string" }
                              }
                            }
                          }
                        },
                        cta: {
                          type: "object",
                          properties: {
                            text: { type: "string" },
                            x: { type: "number" },
                            y: { type: "number" },
                            font: {
                              type: "object",
                              properties: {
                                size: { type: "number", description: "Font size in pixels (18-22)" },
                                weight: { type: "string" },
                                color: { type: "string" }
                              }
                            },
                            background: {
                              type: "object",
                              properties: {
                                style: { type: "string", enum: ["solid", "gradient", "outline"] },
                                color: { type: "string" }
                              }
                            }
                          }
                        },
                        additional_element: {
                          type: "object",
                          properties: {
                            text: { type: "string", description: "Price, discount, or extra info" },
                            x: { type: "number" },
                            y: { type: "number" },
                            font: {
                              type: "object",
                              properties: {
                                size: { type: "number" },
                                weight: { type: "string" },
                                color: { type: "string" }
                              }
                            }
                          }
                        },
                        background: {
                          type: "object",
                          properties: {
                            style: { type: "string", enum: ["gradient", "solid", "pattern"] },
                            colors: { 
                              type: "array", 
                              items: { type: "string" },
                              description: "Array of 2-3 hex colors for gradients"
                            }
                          }
                        }
                      }
                    }
                  },
                  required: ["content_type", "post_type", "main_text", "subtext", "cta_text", "caption", "hashtags", "visual_style_suggestion"]
                }
              }
            },
            required: ["campaign_name", "generated_content"]
          }
        }],
        function_call: { name: "generate_post_content" }
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const result = await response.json();
    const parsedData = JSON.parse(result.choices[0].message.function_call.arguments);

    return NextResponse.json(parsedData);

  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json({ error: error.message || 'Internal Server Error' }, { status: 500 });
  }
}

