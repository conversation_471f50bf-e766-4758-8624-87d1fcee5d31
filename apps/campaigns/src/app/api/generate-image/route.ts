import { NextResponse } from 'next/server';

// Define interfaces for the visual_style_suggestion structure (must match lib/store.ts)
interface FontStyle {
  family?: string;
  size?: number;
  weight?: string;
  color?: string;
}

interface TextElement {
  text: string;
  position?: string;
  font?: FontStyle;
  x?: number;
  y?: number;
}

interface ImageElement {
  src?: string;
  type?: string;
  position?: string;
  size?: string;
  x?: number;
  y?: number;
}

interface BackgroundStyle {
  style?: 'gradient' | 'solid' | 'image' | 'pattern';
  colors?: string[];
  color?: string;
  image_src?: string;
  pattern?: string;
}

interface CTA {
  text: string;
  position?: string;
  font?: FontStyle;
  background?: BackgroundStyle;
  x?: number;
  y?: number;
}

type VisualStyleSuggestion = {
  tag: 'informative';
  heading?: TextElement;
  subheading?: TextElement;
  logo?: ImageElement;
  background?: BackgroundStyle;
  image?: ImageElement;
} | {
  tag: 'product';
  heading?: TextElement;
  subheading?: TextElement;
  product_image?: ImageElement;
  product_price?: TextElement;
  cta?: CTA;
  store_location?: TextElement;
  logo?: ImageElement;
  background?: BackgroundStyle;
} | {
  tag: 'testimonial';
  heading?: TextElement;
  testimonial?: TextElement;
  customer_name?: TextElement;
  rating?: {
    value?: number;
    position?: string;
    style?: string;
    color?: string;
    x?: number;
    y?: number;
  };
  avatar_image?: ImageElement;
  logo?: ImageElement;
  background?: BackgroundStyle;
} | {
  tag: 'announcement';
  title?: TextElement;
  subtitle?: TextElement;
  event_location?: TextElement;
  main_image?: ImageElement;
  cta?: CTA;
  logo?: ImageElement;
  background?: BackgroundStyle;
} | {
  tag: 'offer';
  offer_details?: TextElement;
  discount_percentage?: {
    value?: number;
    position?: string;
    style?: string;
    color?: string;
    background?: string;
    x?: number;
    y?: number;
  };
  code?: TextElement;
  expiration_date?: TextElement;
  cta?: CTA;
  product_image?: ImageElement;
  logo?: ImageElement;
  background?: BackgroundStyle;
};

interface ContentItem {
  content_type: 'post';
  content_idea: string;
  text_overlays: string[];
  caption: string;
  hashtag_suggestions: string[];
  scheduled_datetime: string;
  call_to_action: string;
  visual_style_suggestion?: VisualStyleSuggestion;
}

interface CampaignData {
  campaign_name: string;
  generated_content: ContentItem[];
}

export async function POST(req: Request) {
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    return NextResponse.json({ error: 'OpenAI API key not configured' }, { status: 500 });
  }

  try {
    const { businessName, businessDescription, visualStyle, selectedPostType } = await req.json();

    if (!businessName || !businessDescription || !visualStyle || !selectedPostType) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const campaignLayoutFunction = {
      name: "generate_instagram_campaign_layout",
      description: "Generate structured JSON for an Instagram campaign",
      parameters: {
        type: "object",
        properties: {
          campaign_name: { type: "string" },
          generated_content: {
            type: "array",
            items: {
              type: "object",
              properties: {
                content_type: { type: "string" },
                content_idea: { type: "string" },
                text_overlays: { type: "array", items: { type: "string" } },
                caption: { type: "string" },
                hashtag_suggestions: { type: "array", items: { type: "string" } },
                scheduled_datetime: { type: "string" },
                call_to_action: { type: "string" },
                visual_style_suggestion: { type: "object" }
              }
            }
          }
        }
      }
    };

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-mini",
        messages: [
          { role: "system", content: "You are a brand strategist AI." },
          { role: "user", content: `Generate Instagram campaign for ${businessName}: ${businessDescription}. Style: ${visualStyle}. Post type: ${selectedPostType}` }
        ],
        functions: [campaignLayoutFunction],
        function_call: { name: "generate_instagram_campaign_layout" }
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const result = await response.json();
    const parsedData: CampaignData = JSON.parse(result.choices[0].message.function_call.arguments);

    return NextResponse.json(parsedData);

  } catch (error: any) {
    console.error('API Error:', error);
    return NextResponse.json({ error: error.message || 'Internal Server Error' }, { status: 500 });
  }
}