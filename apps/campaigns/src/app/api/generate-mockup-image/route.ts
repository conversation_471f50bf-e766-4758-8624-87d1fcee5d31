import { NextResponse } from 'next/server';

interface ImageGenerationRequest {
  businessName: string;
  businessDescription: string;
  visualStyle: string;
  selectedPostType: string;
  textOverlays: string[];
  contentIdea: string;
}

export async function POST(req: Request) {
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey) {
    return NextResponse.json({ error: 'OpenAI API key not configured' }, { status: 500 });
  }

  try {
    const { businessName, businessDescription, visualStyle, selectedPostType, textOverlays, contentIdea }: ImageGenerationRequest = await req.json();

    if (!businessName || !selectedPostType || !contentIdea) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Generate background image using DALL-E
    const imagePrompt = `Create a modern Instagram post background for ${businessName}, a ${businessDescription} business. Style: ${visualStyle}. Post type: ${selectedPostType}. The image should be 1080x1080 pixels, visually appealing, and suitable for overlaying text. No text should be included in the image itself.`;

    const dalleResponse = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "dall-e-3",
        prompt: imagePrompt,
        n: 1,
        size: "1024x1024",
        quality: "standard"
      })
    });

    if (!dalleResponse.ok) {
      throw new Error(`DALL-E API error: ${dalleResponse.status}`);
    }

    const dalleResult = await dalleResponse.json();
    const generatedImageUrl = dalleResult.data[0].url;

    // Return the generated image URL along with text overlay data
    return NextResponse.json({
      backgroundImageUrl: generatedImageUrl,
      textOverlays: textOverlays,
      contentIdea: contentIdea,
      postType: selectedPostType
    });

  } catch (error: any) {
    console.error('Image Generation Error:', error);
    return NextResponse.json({ error: error.message || 'Internal Server Error' }, { status: 500 });
  }
}