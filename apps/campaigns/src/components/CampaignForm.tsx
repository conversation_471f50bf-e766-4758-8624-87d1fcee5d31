'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAppStore } from '@/lib/store';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';
import { PostMockupGenerator } from '@/components/PostMockupGenerator';

export function CampaignForm() {
  const {
    businessName,
    setBusinessName,
    businessDescription,
    setBusinessDescription,
    visualStyle,
    setVisualStyle,
    selectedPostType,
    setSelectedPostType,
    campaignData,
    setCampaignData,
    setLoading,
    setError,
    loading,
    error,
  } = useAppStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!businessName.trim() || !businessDescription.trim() || !visualStyle.trim() || !selectedPostType) {
      setError('Please fill in all required fields.');
      return;
    }

    setLoading(true);
    setError(null);
    setCampaignData(null);

    try {
      const response = await fetch('/api/generate-campaign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ businessName, businessDescription, visualStyle, selectedPostType }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setCampaignData(data);
    } catch (err: any) {
      console.error('Error generating campaign:', err);
      setError(err.message || 'An unexpected error occurred.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Form Section */}
      <div className="p-6">
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Campaign Details</h3>
          <p className="text-sm text-gray-600">
            Provide information about your business and campaign to generate targeted Instagram content.
            Our AI will create engaging posts, captions, and hashtags tailored to your brand.
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-red-800">Error generating campaign</h4>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 mb-2">
                Business Name *
              </label>
              <Input
                id="businessName"
                type="text"
                placeholder="e.g., The Bubbles"
                value={businessName}
                onChange={(e) => setBusinessName(e.target.value)}
                required
                className="w-full"
              />
            </div>

            <div>
              <label htmlFor="postType" className="block text-sm font-medium text-gray-700 mb-2">
                Post Type *
              </label>
              <Select value={selectedPostType} onValueChange={setSelectedPostType}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a post type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Informative">Informative</SelectItem>
                  <SelectItem value="Product">Product</SelectItem>
                  <SelectItem value="Testimonial">Testimonial</SelectItem>
                  <SelectItem value="Announcement">Announcement</SelectItem>
                  <SelectItem value="Offer">Offer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <label htmlFor="businessDescription" className="block text-sm font-medium text-gray-700 mb-2">
              Business Description *
            </label>
            <Textarea
              id="businessDescription"
              placeholder="e.g., Handmade aesthetic accessories that bring joy and style to everyday life"
              value={businessDescription}
              onChange={(e) => setBusinessDescription(e.target.value)}
              required
              className="w-full min-h-[100px] resize-none"
            />
          </div>

          <div>
            <label htmlFor="visualStyle" className="block text-sm font-medium text-gray-700 mb-2">
              Campaign Theme / Visual Style *
            </label>
            <Input
              id="visualStyle"
              type="text"
              placeholder="e.g., Summer Sale, Fall Collection, Holiday Special"
              value={visualStyle}
              onChange={(e) => setVisualStyle(e.target.value)}
              required
              className="w-full"
            />
          </div>

          <div className="flex justify-end pt-4 border-t border-gray-200">
            <Button
              type="submit"
              className="px-8 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium disabled:opacity-50"
              disabled={loading || !businessName.trim() || !businessDescription.trim() || !visualStyle.trim() || !selectedPostType}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Campaign...
                </>
              ) : (
                'Generate Campaign'
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* Results Section */}
      {campaignData && (
        <div className="border-t border-gray-200 bg-gray-50">
          <div className="p-6">
            {/* Success Message */}
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-green-800">Campaign generated successfully!</h4>
                <p className="text-sm text-green-700 mt-1">
                  Your Instagram campaign content is ready. You can now download or customize each post.
                </p>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Generated Content</h3>
              <p className="text-sm text-gray-600">
                Campaign: <span className="font-medium">{campaignData.campaign_name}</span>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {campaignData.generated_content.map((item, index) => (
                <PostMockupGenerator
                  key={index}
                  content={item}
                  businessName={businessName}
                />
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}


