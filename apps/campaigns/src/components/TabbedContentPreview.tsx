'use client';

import React, { useState } from 'react';
import { useAppStore } from '@/lib/store';
import { PostCard } from './PostCard';
import { CarouselCard } from './CarouselCard';
import { StoryCard } from './StoryCard';
import { ImagePreviewModal } from './ImagePreviewModal';

type TabType = 'story' | 'carousel' | 'post' | 'post-review';

export function TabbedContentPreview() {
  const { campaignData, loading, error } = useAppStore();
  const [activeTab, setActiveTab] = useState<TabType>('post');
  const [selectedContent, setSelectedContent] = useState<any>(null);
  const [showImageModal, setShowImageModal] = useState(false);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Generating content, please wait...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200">
          <p className="text-lg text-red-600">Error: {error}</p>
        </div>
      </div>
    );
  }

  if (!campaignData || campaignData.generated_content.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center p-6 bg-gray-50 rounded-lg border border-gray-200">
          <p className="text-lg text-gray-500">No campaign content generated yet.</p>
          <p className="text-sm text-gray-400 mt-2">Fill the form and click "Generate Campaign"</p>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'story' as TabType, label: 'Story review', count: campaignData.generated_content.filter(item => item.content_type === 'story').length },
    { id: 'carousel' as TabType, label: 'Carousel review', count: campaignData.generated_content.filter(item => item.content_type === 'carousel').length },
    { id: 'post' as TabType, label: 'Post Preview', count: campaignData.generated_content.filter(item => item.content_type === 'post').length },
    { id: 'post-review' as TabType, label: 'Post review', count: campaignData.generated_content.filter(item => item.content_type === 'post').length }
  ];

  const filteredContent = campaignData.generated_content.filter(item => {
    if (activeTab === 'post-review') return item.content_type === 'post';
    return item.content_type === activeTab;
  });

  const handleContentClick = (content: any) => {
    setSelectedContent(content);
    setShowImageModal(true);
  };

  return (
    <div className="space-y-4">
      {/* Campaign Name */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <h3 className="text-lg font-semibold text-blue-900">
          Campaign: {campaignData.campaign_name}
        </h3>
      </div>

      {/* Tabs */}
      <div className="bg-gray-100 p-2 rounded-lg">
        <div className="grid grid-cols-2 gap-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-3 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-gray-800 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="space-y-4 max-h-[500px] overflow-y-auto">
        {filteredContent.map((item, index) => (
          <div 
            key={index} 
            className="cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => handleContentClick(item)}
          >
            {activeTab === 'story' && (
              <StoryCard
                contentIdea={item.content_idea as string}
                textOverlays={item.text_overlays}
                caption={item.caption}
                hashtagSuggestions={item.hashtag_suggestions}
                scheduledDatetime={item.scheduled_datetime}
                callToAction={item.call_to_action}
              />
            )}
            {activeTab === 'carousel' && (
              <CarouselCard
                contentIdea={item.content_idea as { id: string; text: string }[]}
                textOverlays={item.text_overlays}
                caption={item.caption}
                hashtagSuggestions={item.hashtag_suggestions}
                scheduledDatetime={item.scheduled_datetime}
                callToAction={item.call_to_action}
              />
            )}
            {(activeTab === 'post' || activeTab === 'post-review') && (
              <PostCard
                contentIdea={item.content_idea as string}
                textOverlays={item.text_overlays}
                caption={item.caption}
                hashtagSuggestions={item.hashtag_suggestions}
                scheduledDatetime={item.scheduled_datetime}
                callToAction={item.call_to_action}
              />
            )}
          </div>
        ))}
      </div>

      {/* Image Preview Modal */}
      {showImageModal && selectedContent && (
        <ImagePreviewModal
          content={selectedContent}
          onClose={() => setShowImageModal(false)}
        />
      )}
    </div>
  );
}
