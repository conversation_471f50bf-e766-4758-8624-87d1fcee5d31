'use client';

import React from 'react';
import { useAppStore } from '@/lib/store';
import { PostCard } from '@/components/PostCard';
import { Loader2 } from 'lucide-react';

export function ContentPreview() {
  const { campaignData, loading, error } = useAppStore();

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
        <p className="text-gray-600">Generating your campaign...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (!campaignData) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Generated Content</h2>
        <h3 className="text-xl text-gray-600">Campaign: {campaignData.campaign_name}</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {campaignData.generated_content.map((item, index) => (
          <PostCard
            key={index}
            contentIdea={item.content_idea}
            visualStyleSuggestion={item.visual_style_suggestion}
          />
        ))}
      </div>
    </div>
  );
}

