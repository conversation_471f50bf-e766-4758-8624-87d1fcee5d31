import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { VisualStyleSuggestion } from '@/lib/store'; // Import the new type

interface StoryCardProps {
  contentIdea: string;
  textOverlays: string[];
  caption: string;
  hashtagSuggestions: string[];
  scheduledDatetime: string;
  callToAction: string;
  visualStyleSuggestion?: VisualStyleSuggestion; // New prop
}

// Helper function to get dynamic styles for text elements
const getTextStyle = (element?: { font?: { family?: string; size?: number; weight?: string; color?: string; } }): React.CSSProperties => {
  if (!element?.font) return {};
  return {
    fontFamily: element.font.family || 'Inter, sans-serif',
    fontSize: element.font.size ? `${element.font.size}px` : undefined,
    fontWeight: element.font.weight || undefined,
    color: element.font.color || undefined,
  };
};

// Helper function to get dynamic styles for positioning
const getPositionClasses = (position?: string) => {
  switch (position) {
    case 'top-left': return 'absolute top-4 left-4';
    case 'top-center': return 'absolute top-4 left-1/2 -translate-x-1/2';
    case 'top-right': return 'absolute top-4 right-4';
    case 'center': return 'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2';
    case 'center-left': return 'absolute top-1/2 left-4 -translate-y-1/2';
    case 'center-right': return 'absolute top-1/2 right-4 -translate-y-1/2';
    case 'bottom-left': return 'absolute bottom-4 left-4';
    case 'bottom-center': return 'absolute bottom-4 left-1/2 -translate-x-1/2';
    case 'bottom-right': return 'absolute bottom-4 right-4';
    case 'below-heading': return 'mt-2';
    case 'below-testimonial': return 'mt-2';
    case 'below-offer-details': return 'mt-2';
    default: return '';
  }
};

// Helper function to get background styles
const getBackgroundStyle = (background?: { style?: string; colors?: string[]; color?: string; image_src?: string; pattern?: string }): React.CSSProperties => {
  if (!background) return {};
  if (background.style === 'gradient' && background.colors && background.colors.length >= 2) {
    return {
      background: `linear-gradient(to right, ${background.colors[0]}, ${background.colors[1]})`,
    };
  }
  if (background.style === 'solid' && background.color) {
    return { backgroundColor: background.color };
  }
  if (background.style === 'image' && background.image_src) {
    return {
      backgroundImage: `url(https://placehold.co/600x400/${background.image_src.split('.')[0].replace('#', '')}/ffffff?text=BG)`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    };
  }
  if (background.style === 'pattern') {
      return { backgroundImage: `url(https://www.transparenttextures.com/patterns/${background.pattern || 'diagmonds'}.png)`, backgroundSize: 'repeat' };
  }
  return {};
};

export function StoryCard({
  contentIdea,
  textOverlays,
  caption,
  hashtagSuggestions,
  scheduledDatetime,
  callToAction,
  visualStyleSuggestion, // Destructure new prop
}: StoryCardProps) {
  const isInformative = visualStyleSuggestion?.tag === 'informative';
  const isProduct = visualStyleSuggestion?.tag === 'product';
  const isTestimonial = visualStyleSuggestion?.tag === 'testimonial';
  const isAnnouncement = visualStyleSuggestion?.tag === 'announcement';
  const isOffer = visualStyleSuggestion?.tag === 'offer';

  return (
    <Card className="w-full max-w-md mx-auto shadow-md rounded-xl overflow-hidden bg-white">
      <CardHeader className="bg-gradient-to-r from-cyan-500 to-blue-500 p-4 text-white">
        <CardTitle className="text-xl font-bold">Instagram Story</CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-4">
        <div className="bg-gray-100 p-4 rounded-lg">
          <p className="text-sm font-medium text-gray-700 mb-2">Content Idea:</p>
          <p className="text-base text-gray-900">{contentIdea}</p>
        </div>
        <div className="relative w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden" style={getBackgroundStyle(visualStyleSuggestion?.background)}>
          {/* Render elements based on visualStyleSuggestion */}
          {isInformative && visualStyleSuggestion && (
            <>
              {visualStyleSuggestion.heading && (
                <h3 className={`text-2xl font-bold ${getPositionClasses(visualStyleSuggestion.heading.position)}`} style={getTextStyle(visualStyleSuggestion.heading)}>
                  {visualStyleSuggestion.heading.text}
                </h3>
              )}
              {visualStyleSuggestion.subheading && (
                <p className={`text-lg ${getPositionClasses(visualStyleSuggestion.subheading.position)}`} style={getTextStyle(visualStyleSuggestion.subheading)}>
                  {visualStyleSuggestion.subheading.text}
                </p>
              )}
              {visualStyleSuggestion.image && (
                <img
                  src={`https://placehold.co/180x180/cccccc/ffffff?text=StoryImg`}
                  alt="Informative Visual"
                  className={`w-auto h-auto max-w-[80%] max-h-[80%] object-contain ${getPositionClasses(visualStyleSuggestion.image.position)}`}
                />
              )}
              {visualStyleSuggestion.logo && (
                <img
                  src={`https://placehold.co/40x40/cccccc/ffffff?text=Logo`}
                  alt="Brand Logo"
                  className={`w-10 h-10 rounded-full ${getPositionClasses(visualStyleSuggestion.logo.position)}`}
                />
              )}
            </>
          )}

          {isProduct && visualStyleSuggestion && (
            <>
              {visualStyleSuggestion.heading && (
                <h3 className={`text-2xl font-bold ${getPositionClasses(visualStyleSuggestion.heading.position)}`} style={getTextStyle(visualStyleSuggestion.heading)}>
                  {visualStyleSuggestion.heading.text}
                </h3>
              )}
              {visualStyleSuggestion.subheading && (
                <p className={`text-lg ${getPositionClasses(visualStyleSuggestion.subheading.position)}`} style={getTextStyle(visualStyleSuggestion.subheading)}>
                  {visualStyleSuggestion.subheading.text}
                </p>
              )}
              {visualStyleSuggestion.product_image && (
                <img
                  src={`https://placehold.co/200x200/cccccc/ffffff?text=Product`}
                  alt="Product Visual"
                  className={`w-auto h-auto max-w-[80%] max-h-[80%] object-contain ${getPositionClasses(visualStyleSuggestion.product_image.position)}`}
                />
              )}
              {visualStyleSuggestion.product_price && (
                <span className={`text-xl font-bold ${getPositionClasses(visualStyleSuggestion.product_price.position)}`} style={getTextStyle(visualStyleSuggestion.product_price)}>
                  {visualStyleSuggestion.product_price.text}
                </span>
              )}
              {visualStyleSuggestion.cta && (
                <div className={`${getPositionClasses(visualStyleSuggestion.cta.position)}`}>
                  <button className="px-4 py-2 rounded-md text-white font-semibold" style={{ ...getTextStyle(visualStyleSuggestion.cta), backgroundColor: visualStyleSuggestion.cta.background?.color }}>
                    {visualStyleSuggestion.cta.text}
                  </button>
                </div>
              )}
              {visualStyleSuggestion.store_location && (
                <p className={`text-sm ${getPositionClasses(visualStyleSuggestion.store_location.position)}`} style={getTextStyle(visualStyleSuggestion.store_location)}>
                  {visualStyleSuggestion.store_location.text}
                </p>
              )}
              {visualStyleSuggestion.logo && (
                <img
                  src={`https://placehold.co/40x40/cccccc/ffffff?text=Logo`}
                  alt="Brand Logo"
                  className={`w-10 h-10 rounded-full ${getPositionClasses(visualStyleSuggestion.logo.position)}`}
                />
              )}
            </>
          )}

          {isTestimonial && visualStyleSuggestion && (
            <>
              {visualStyleSuggestion.heading && (
                <h3 className={`text-2xl font-bold ${getPositionClasses(visualStyleSuggestion.heading.position)}`} style={getTextStyle(visualStyleSuggestion.heading)}>
                  {visualStyleSuggestion.heading.text}
                </h3>
              )}
              {visualStyleSuggestion.testimonial && (
                <p className={`text-lg italic text-center px-4 ${getPositionClasses(visualStyleSuggestion.testimonial.position)}`} style={getTextStyle(visualStyleSuggestion.testimonial)}>
                  &quot;{visualStyleSuggestion.testimonial.text}&quot;
                </p>
              )}
              {visualStyleSuggestion.customer_name && (
                <p className={`text-base font-medium ${getPositionClasses(visualStyleSuggestion.customer_name.position)}`} style={getTextStyle(visualStyleSuggestion.customer_name)}>
                  - {visualStyleSuggestion.customer_name.text}
                </p>
              )}
              {visualStyleSuggestion.rating && (
                <div className={`flex ${getPositionClasses(visualStyleSuggestion.rating.position)}`} style={{ color: visualStyleSuggestion.rating.color }}>
                  {Array.from({ length: visualStyleSuggestion.rating.value || 0 }).map((_, i) => (
                    <span key={i} className="text-xl">&#9733;</span>
                  ))}
                </div>
              )}
              {visualStyleSuggestion.avatar_image && (
                <img
                  src={`https://placehold.co/60x60/cccccc/ffffff?text=Avatar`}
                  alt="Customer Avatar"
                  className={`w-16 h-16 rounded-full object-cover ${getPositionClasses(visualStyleSuggestion.avatar_image.position)}`}
                />
              )}
              {visualStyleSuggestion.logo && (
                <img
                  src={`https://placehold.co/40x40/cccccc/ffffff?text=Logo`}
                  alt="Brand Logo"
                  className={`w-10 h-10 rounded-full ${getPositionClasses(visualStyleSuggestion.logo.position)}`}
                />
              )}
            </>
          )}

          {isAnnouncement && visualStyleSuggestion && (
            <>
              {visualStyleSuggestion.title && (
                <h3 className={`text-3xl font-bold text-center ${getPositionClasses(visualStyleSuggestion.title.position)}`} style={getTextStyle(visualStyleSuggestion.title)}>
                  {visualStyleSuggestion.title.text}
                </h3>
              )}
              {visualStyleSuggestion.subtitle && (
                <p className={`text-xl text-center ${getPositionClasses(visualStyleSuggestion.subtitle.position)}`} style={getTextStyle(visualStyleSuggestion.subtitle)}>
                  {visualStyleSuggestion.subtitle.text}
                </p>
              )}
              {visualStyleSuggestion.main_image && (
                <img
                  src={`https://placehold.co/250x150/cccccc/ffffff?text=Event`}
                  alt="Announcement Visual"
                  className={`w-auto h-auto max-w-[90%] max-h-[90%] object-contain ${getPositionClasses(visualStyleSuggestion.main_image.position)}`}
                />
              )}
              {visualStyleSuggestion.event_location && (
                <p className={`text-base ${getPositionClasses(visualStyleSuggestion.event_location.position)}`} style={getTextStyle(visualStyleSuggestion.event_location)}>
                  {visualStyleSuggestion.event_location.text}
                </p>
              )}
              {visualStyleSuggestion.cta && (
                <div className={`${getPositionClasses(visualStyleSuggestion.cta.position)}`}>
                  <button className="px-4 py-2 rounded-md text-white font-semibold" style={{ ...getTextStyle(visualStyleSuggestion.cta), backgroundColor: visualStyleSuggestion.cta.background?.color }}>
                    {visualStyleSuggestion.cta.text}
                  </button>
                </div>
              )}
              {visualStyleSuggestion.logo && (
                <img
                  src={`https://placehold.co/40x40/cccccc/ffffff?text=Logo`}
                  alt="Brand Logo"
                  className={`w-10 h-10 rounded-full ${getPositionClasses(visualStyleSuggestion.logo.position)}`}
                />
              )}
            </>
          )}

          {isOffer && visualStyleSuggestion && (
            <>
              {visualStyleSuggestion.offer_details && (
                <h3 className={`text-3xl font-bold text-center ${getPositionClasses(visualStyleSuggestion.offer_details.position)}`} style={getTextStyle(visualStyleSuggestion.offer_details)}>
                  {visualStyleSuggestion.offer_details.text}
                </h3>
              )}
              {visualStyleSuggestion.discount_percentage && (
                <div className={`px-4 py-2 rounded-full font-bold text-white ${getPositionClasses(visualStyleSuggestion.discount_percentage.position)}`} style={{ backgroundColor: visualStyleSuggestion.discount_percentage.background, color: visualStyleSuggestion.discount_percentage.color }}>
                  {visualStyleSuggestion.discount_percentage.value}% OFF
                </div>
              )}
              {visualStyleSuggestion.code && (
                <p className={`text-xl font-medium ${getPositionClasses(visualStyleSuggestion.code.position)}`} style={getTextStyle(visualStyleSuggestion.code)}>
                  {visualStyleSuggestion.code.text}
                </p>
              )}
              {visualStyleSuggestion.expiration_date && (
                <p className={`text-sm ${getPositionClasses(visualStyleSuggestion.expiration_date.position)}`} style={getTextStyle(visualStyleSuggestion.expiration_date)}>
                  {visualStyleSuggestion.expiration_date.text}
                </p>
              )}
              {visualStyleSuggestion.cta && (
                <div className={`${getPositionClasses(visualStyleSuggestion.cta.position)}`}>
                  <button className="px-4 py-2 rounded-md text-white font-semibold" style={{ ...getTextStyle(visualStyleSuggestion.cta), backgroundColor: visualStyleSuggestion.cta.background?.color }}>
                    {visualStyleSuggestion.cta.text}
                  </button>
                </div>
              )}
              {visualStyleSuggestion.product_image && (
                <img
                  src={`https://placehold.co/180x180/cccccc/ffffff?text=Product`}
                  alt="Offer Product"
                  className={`w-auto h-auto max-w-[80%] max-h-[80%] object-contain ${getPositionClasses(visualStyleSuggestion.product_image.position)}`}
                />
              )}
              {visualStyleSuggestion.logo && (
                <img
                  src={`https://placehold.co/40x40/cccccc/ffffff?text=Logo`}
                  alt="Brand Logo"
                  className={`w-10 h-10 rounded-full ${getPositionClasses(visualStyleSuggestion.logo.position)}`}
                />
              )}
            </>
          )}

          {/* Fallback if no specific visual style is applied */}
          {!visualStyleSuggestion && (
            <div className="absolute inset-0 flex flex-col justify-end p-4 text-white text-center bg-black bg-opacity-40">
              {textOverlays.map((overlay, index) => (
                <p key={index} className="text-lg font-bold">
                  {overlay}
                </p>
              ))}
            </div>
          )}
        </div>
        <div>
          <p className="text-sm font-medium text-gray-700 mb-1">Caption:</p>
          <p className="text-base text-gray-800">{caption}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-700 mb-1">Hashtags:</p>
          <div className="flex flex-wrap gap-2">
            {hashtagSuggestions.map((tag, index) => (
              <span key={index} className="px-3 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">
                {tag}
              </span>
            ))}
          </div>
        </div>
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>Scheduled: {new Date(scheduledDatetime).toLocaleString()}</span>
          <span className="font-semibold text-blue-700">Action: {callToAction}</span>
        </div>
      </CardContent>
    </Card>
  );
}

