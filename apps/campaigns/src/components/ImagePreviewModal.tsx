'use client';

import React from 'react';
import { X } from 'lucide-react';
import { ImagePreviewEditor } from './ImagePreviewEditor';

interface ImagePreviewModalProps {
  content: any;
  onClose: () => void;
}

export function ImagePreviewModal({ content, onClose }: ImagePreviewModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Edit Post Image</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-4">
          <ImagePreviewEditor
            initialContentIdea={typeof content.content_idea === 'string' ? content.content_idea : content.content_idea?.heading?.text || ''}
            initialTextOverlays={content.text_overlays || []}
          />
        </div>
      </div>
    </div>
  );
}



