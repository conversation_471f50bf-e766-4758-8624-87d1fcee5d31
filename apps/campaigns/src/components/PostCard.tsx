'use client';

import React, { useRef, useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, Expand, Settings2, X } from 'lucide-react';
import html2canvas from 'html2canvas';
import { VisualStyleSuggestion, TextElement, ImageElement, CTA } from '@/lib/store';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface PostCardProps {
  contentIdea: string;
  visualStyleSuggestion?: VisualStyleSuggestion;
}

// DraggableElement component
interface DraggableElementProps {
  children: React.ReactNode;
  initialX: number;
  initialY: number;
  onDragEnd: (newX: number, newY: number) => void;
  isEditMode: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const DraggableElement: React.FC<DraggableElementProps> = ({ 
  children, 
  initialX, 
  initialY, 
  onDragEnd, 
  isEditMode, 
  className, 
  style 
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [pos, setPos] = useState({ x: initialX, y: initialY });
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setPos({ x: initialX, y: initialY });
  }, [initialX, initialY]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!isEditMode) return;
    setIsDragging(true);
    setOffset({
      x: e.clientX - pos.x,
      y: e.clientY - pos.y,
    });
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (!isEditMode) return;
    setIsDragging(true);
    const touch = e.touches[0];
    setOffset({
      x: touch.clientX - pos.x,
      y: touch.clientY - pos.y,
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    setPos({
      x: e.clientX - offset.x,
      y: e.clientY - offset.y,
    });
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging) return;
    const touch = e.touches[0];
    setPos({
      x: touch.clientX - offset.x,
      y: touch.clientY - offset.y,
    });
  };

  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false);
      onDragEnd(pos.x, pos.y);
    }
  };

  const handleTouchEnd = () => {
    if (isDragging) {
      setIsDragging(false);
      onDragEnd(pos.x, pos.y);
    }
  };

  useEffect(() => {
    if (isEditMode) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleTouchEnd);
    }
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);
    };
  }, [isDragging, isEditMode, pos, offset]);

  return (
    <div
      ref={elementRef}
      className={`${className} ${isEditMode ? 'cursor-grab border-2 border-dashed border-blue-500 rounded-md' : ''} ${isDragging ? 'cursor-grabbing' : ''}`}
      style={{
        position: 'absolute',
        left: pos.x,
        top: pos.y,
        zIndex: isDragging ? 100 : 10,
        transform: 'translate(-50%, -50%)',
        ...style
      }}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
    >
      {children}
    </div>
  );
};

// Helper functions
const getTextStyle = (element?: { font?: { family?: string; size?: number; weight?: string; color?: string; } }): React.CSSProperties => {
  if (!element?.font) return {};
  return {
    fontFamily: element.font.family || 'Inter, sans-serif',
    fontSize: element.font.size ? `${element.font.size}px` : undefined,
    fontWeight: element.font.weight || undefined,
    color: element.font.color || undefined,
  };
};

const getPositionClasses = (position?: string) => {
  switch (position) {
    case 'top-left': return 'absolute top-4 left-4';
    case 'top-center': return 'absolute top-4 left-1/2 -translate-x-1/2';
    case 'top-right': return 'absolute top-4 right-4';
    case 'center': return 'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2';
    case 'center-left': return 'absolute top-1/2 left-4 -translate-y-1/2';
    case 'center-right': return 'absolute top-1/2 right-4 -translate-y-1/2';
    case 'bottom-left': return 'absolute bottom-4 left-4';
    case 'bottom-center': return 'absolute bottom-4 left-1/2 -translate-x-1/2';
    case 'bottom-right': return 'absolute bottom-4 right-4';
    case 'below-heading': return 'mt-2';
    case 'below-testimonial': return 'mt-2';
    case 'below-offer-details': return 'mt-2';
    case 'middle-center': return 'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2';
    default: return '';
  }
};

const getBackgroundStyle = (background?: { style?: string; colors?: string[]; color?: string; image_src?: string; pattern?: string }): React.CSSProperties => {
  if (!background) return {};
  if (background.style === 'gradient' && background.colors && background.colors.length >= 2) {
    return {
      background: `linear-gradient(to right, ${background.colors[0]}, ${background.colors[1]})`,
    };
  }
  if (background.style === 'solid' && background.color) {
    return { backgroundColor: background.color };
  }
  if (background.style === 'image' && background.image_src) {
    const placeholderColor = background.image_src.split('.')[0].replace(/[^a-fA-F0-9]/g, '').substring(0, 6) || 'cccccc';
    return {
      backgroundImage: `url(https://placehold.co/600x400/${placeholderColor}/ffffff?text=BG)`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    };
  }
  if (background.style === 'pattern') {
    return { 
      backgroundImage: `url(https://www.transparenttextures.com/patterns/${background.pattern || 'diagmonds'}.png)`, 
      backgroundSize: 'repeat' 
    };
  }
  return {};
};

export function PostCard({
  contentIdea,
  visualStyleSuggestion,
}: PostCardProps) {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editableVisualStyle, setEditableVisualStyle] = useState<VisualStyleSuggestion | undefined>(visualStyleSuggestion);

  useEffect(() => {
    setEditableVisualStyle(visualStyleSuggestion);
  }, [visualStyleSuggestion]);

  const updateElementPosition = (elementKey: string, newX: number, newY: number) => {
    if (!editableVisualStyle) return;
    
    setEditableVisualStyle(prev => {
      if (!prev) return prev;
      const element = prev[elementKey as keyof typeof prev];
      return {
        ...prev,
        [elementKey]: {
          ...(typeof element === 'object' && element !== null ? element : {}),
          x: newX,
          y: newY,
        }
      } as VisualStyleSuggestion;
    });
  };

  const handleDownloadImage = async () => {
    if (cardRef.current) {
      const downloadButton = cardRef.current.querySelector('.download-button');
      const expandButton = cardRef.current.querySelector('.expand-button');
      const editToggle = cardRef.current.querySelector('.edit-toggle-button');
      const draggableBorders = cardRef.current.querySelectorAll('.border-dashed');

      if (downloadButton) (downloadButton as HTMLElement).style.visibility = 'hidden';
      if (expandButton) (expandButton as HTMLElement).style.visibility = 'hidden';
      if (editToggle) (editToggle as HTMLElement).style.visibility = 'hidden';
      draggableBorders.forEach(el => (el as HTMLElement).style.borderStyle = 'none');

      const originalEditMode = isEditMode;
      setIsEditMode(false);

      await new Promise(resolve => setTimeout(resolve, 100));

      const canvas = await html2canvas(cardRef.current, {
        useCORS: true,
        logging: true,
        scale: 2,
        backgroundColor: null,
      });

      if (downloadButton) (downloadButton as HTMLElement).style.visibility = 'visible';
      if (expandButton) (expandButton as HTMLElement).style.visibility = 'visible';
      if (editToggle) (editToggle as HTMLElement).style.visibility = 'visible';
      draggableBorders.forEach(el => (el as HTMLElement).style.borderStyle = 'dashed');
      setIsEditMode(originalEditMode);

      const image = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = image;
      link.download = `${editableVisualStyle?.tag || 'instagram-post'}-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const tag = editableVisualStyle?.tag;

  const renderVisualContent = (isDialogView = false) => {
    if (!editableVisualStyle || !tag) {
      const fallbackBackground = {
        style: 'gradient',
        colors: ['#a8c0ff', '#3f2b96'],
      };
      return (
        <div className="absolute inset-0 flex flex-col items-center justify-center text-white text-center p-4" style={getBackgroundStyle(fallbackBackground)}>
          <p className="text-xl font-bold mb-2">Generated Visual</p>
          <p className="text-base">{contentIdea}</p>
          <p className="text-sm mt-2">(AI style suggestion not fully provided)</p>
        </div>
      );
    }

    switch (tag) {
      case 'informative':
        return (
          <>
            {editableVisualStyle.heading && (
              <DraggableElement
                initialX={editableVisualStyle.heading.x || 0}
                initialY={editableVisualStyle.heading.y || 0}
                onDragEnd={(x, y) => updateElementPosition('heading', x, y)}
                isEditMode={isEditMode}
                className={getPositionClasses(editableVisualStyle.heading.position)}
              >
                <h3 className="text-2xl font-bold" style={getTextStyle(editableVisualStyle.heading)}>
                  {editableVisualStyle.heading.text}
                </h3>
              </DraggableElement>
            )}
            {editableVisualStyle.subheading && (
              <DraggableElement
                initialX={editableVisualStyle.subheading.x || 0}
                initialY={editableVisualStyle.subheading.y || 0}
                onDragEnd={(x, y) => updateElementPosition('subheading', x, y)}
                isEditMode={isEditMode}
                className={getPositionClasses(editableVisualStyle.subheading.position)}
              >
                <p className="text-lg" style={getTextStyle(editableVisualStyle.subheading)}>
                  {editableVisualStyle.subheading.text}
                </p>
              </DraggableElement>
            )}
          </>
        );
      default:
        return (
          <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-500 text-center p-4 bg-gray-100">
            <p className="text-lg font-medium text-gray-700 mb-2">Post Type: {tag}</p>
            <p className="text-base text-gray-900">{contentIdea}</p>
          </div>
        );
    }
  };

  return (
    <Card className="w-full max-w-sm mx-auto shadow-md rounded-xl overflow-hidden bg-white relative">
      <div
        className="relative w-full aspect-square flex flex-col items-center justify-center rounded-xl overflow-hidden p-4"
        style={getBackgroundStyle(editableVisualStyle?.background)}
      >
        <div ref={cardRef} className="relative w-full h-full flex flex-col items-center justify-center">
          {renderVisualContent()}
        </div>

        <div className="absolute top-2 right-2 z-30 flex space-x-2">
          <Button
            onClick={() => setIsEditMode(!isEditMode)}
            className="edit-toggle-button p-2 rounded-full bg-gray-700 hover:bg-gray-800 text-white shadow-lg"
            title={isEditMode ? "Exit Edit Mode" : "Enter Edit Mode"}
          >
            {isEditMode ? <X className="h-5 w-5" /> : <Settings2 className="h-5 w-5" />}
          </Button>
          <Button
            onClick={handleDownloadImage}
            className="download-button p-2 rounded-full bg-blue-500 hover:bg-blue-600 text-white shadow-lg"
            title="Download Image"
          >
            <Download className="h-5 w-5" />
          </Button>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              className="expand-button absolute bottom-4 left-4 z-20 p-2 rounded-full bg-gray-700 hover:bg-gray-800 text-white shadow-lg"
              title="Expand View"
            >
              <Expand className="h-5 w-5" />
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[800px] w-full h-auto max-h-[90vh] p-0 overflow-hidden">
            <DialogHeader className="p-4 pb-0">
              <DialogTitle className="text-xl font-bold">Generated Post Image</DialogTitle>
              <DialogDescription>
                Full-size preview of your {editableVisualStyle?.tag || 'Instagram'} post.
              </DialogDescription>
            </DialogHeader>
            <div className="relative w-full aspect-square flex items-center justify-center bg-gray-100">
              <div className="relative w-full h-full flex flex-col items-center justify-center" style={getBackgroundStyle(editableVisualStyle?.background)}>
                {renderVisualContent(true)}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </Card>
  );
}
