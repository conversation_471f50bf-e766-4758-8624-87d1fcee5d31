'use client';

import React, { useState } from 'react';
import { LoginForm as SharedLoginForm } from '@repo/ui/login-form';
import { useAuth } from '@repo/auth';

export function LoginForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login, error } = useAuth();

  const handleSubmit = async (email: string, password: string) => {
    setIsSubmitting(true);

    try {
      console.log('LoginForm: Attempting login with:', email);
      const result = await login(email, password);
      console.log('LoginForm: Login successful:', result);
    } catch (error) {
      console.error('LoginForm: Login failed:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SharedLoginForm
      onSubmit={handleSubmit}
      isLoading={isSubmitting}
      error={error}
      title="Campaign Management Login"
    />
  );
}