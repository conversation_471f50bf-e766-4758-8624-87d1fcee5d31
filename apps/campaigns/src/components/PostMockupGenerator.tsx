'use client';

import React, { useRef, useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Download, Edit3, Save, RotateCcw, Type, Palette, Move, Undo2, X } from 'lucide-react';
import html2canvas from 'html2canvas';

interface TextElement {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  fontWeight: string;
  textAlign: string;
  fontFamily?: string;
}

interface PostContent {
  post_type: string;
  main_text: string;
  subtext: string;
  cta_text: string;
  additional_text?: string;
  caption: string;
  hashtags: string[];
  visual_style_suggestion?: {
    tag: string;
    heading?: {
      text: string;
      x: number;
      y: number;
      font: {
        size: number;
        weight: string;
        color: string;
      };
    };
    subheading?: {
      text: string;
      x: number;
      y: number;
      font: {
        size: number;
        weight: string;
        color: string;
      };
    };
    cta?: {
      text: string;
      x: number;
      y: number;
      font: {
        size: number;
        weight: string;
        color: string;
      };
      background?: {
        style: string;
        color: string;
      };
    };
    additional_element?: {
      text: string;
      x: number;
      y: number;
      font: {
        size: number;
        weight: string;
        color: string;
      };
    };
    background?: {
      style: string;
      colors: string[];
    };
  };
}

interface PostMockupProps {
  content: PostContent;
  businessName: string;
}

export function PostMockupGenerator({ content, businessName }: PostMockupProps) {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [textElements, setTextElements] = useState<TextElement[]>([]);
  const [originalElements, setOriginalElements] = useState<TextElement[]>([]);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [draggedElement, setDraggedElement] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  // Force proper text positioning with fixed layout
  const getInitialTextPlacement = (content: PostContent): TextElement[] => {
    if (content.visual_style_suggestion) {
      const elements: TextElement[] = [];
      const vs = content.visual_style_suggestion;

      // Fixed positioning to prevent overlaps - ignore AI coordinates for now
      const positions = {
        heading: { x: 50, y: 20 },
        subheading: { x: 50, y: 40 },
        additional: { x: 50, y: 60 },
        cta: { x: 50, y: 85 }
      };

      if (vs.heading) {
        elements.push({
          id: 'main',
          text: vs.heading.text,
          x: positions.heading.x,
          y: positions.heading.y,
          fontSize: Math.min(Math.max(vs.heading.font.size, 28), 36), // Clamp font size
          color: vs.heading.font.color,
          fontWeight: vs.heading.font.weight,
          textAlign: 'center'
        });
      }

      if (vs.subheading) {
        elements.push({
          id: 'sub',
          text: vs.subheading.text,
          x: positions.subheading.x,
          y: positions.subheading.y,
          fontSize: Math.min(Math.max(vs.subheading.font.size, 16), 22), // Clamp font size
          color: vs.subheading.font.color,
          fontWeight: vs.subheading.font.weight,
          textAlign: 'center'
        });
      }

      if (vs.additional_element) {
        elements.push({
          id: 'additional',
          text: vs.additional_element.text,
          x: positions.additional.x,
          y: positions.additional.y,
          fontSize: Math.min(Math.max(vs.additional_element.font.size, 18), 26), // Clamp font size
          color: vs.additional_element.font.color,
          fontWeight: vs.additional_element.font.weight,
          textAlign: 'center'
        });
      }

      if (vs.cta) {
        elements.push({
          id: 'cta',
          text: vs.cta.text,
          x: positions.cta.x,
          y: positions.cta.y,
          fontSize: Math.min(Math.max(vs.cta.font.size, 16), 20), // Clamp font size
          color: vs.cta.font.color,
          fontWeight: vs.cta.font.weight,
          textAlign: 'center'
        });
      }

      return elements;
    }
    
    // Fallback positioning if no AI suggestion
    const baseElements: TextElement[] = [];
    switch (content.post_type.toLowerCase()) {
      case 'offer':
        baseElements.push(
          { id: 'main', text: content.main_text, x: 50, y: 18, fontSize: 34, color: '#ffffff', fontWeight: 'bold', textAlign: 'center' },
          { id: 'sub', text: content.subtext, x: 50, y: 42, fontSize: 18, color: '#ffffff', fontWeight: 'normal', textAlign: 'center' },
          { id: 'additional', text: content.additional_text || 'Limited Time!', x: 50, y: 62, fontSize: 22, color: '#ffff00', fontWeight: 'bold', textAlign: 'center' },
          { id: 'cta', text: content.cta_text, x: 50, y: 85, fontSize: 18, color: '#000000', fontWeight: 'bold', textAlign: 'center' }
        );
        break;
      case 'announcement':
        baseElements.push(
          { id: 'main', text: content.main_text, x: 50, y: 20, fontSize: 32, color: '#ffffff', fontWeight: 'bold', textAlign: 'center' },
          { id: 'sub', text: content.subtext, x: 50, y: 45, fontSize: 18, color: '#ffffff', fontWeight: 'normal', textAlign: 'center' },
          { id: 'additional', text: content.additional_text || '', x: 50, y: 65, fontSize: 20, color: '#ffffff', fontWeight: 'normal', textAlign: 'center' },
          { id: 'cta', text: content.cta_text, x: 50, y: 85, fontSize: 16, color: '#ffffff', fontWeight: 'bold', textAlign: 'center' }
        );
        break;
      default:
        baseElements.push(
          { id: 'main', text: content.main_text, x: 50, y: 22, fontSize: 30, color: '#ffffff', fontWeight: 'bold', textAlign: 'center' },
          { id: 'sub', text: content.subtext, x: 50, y: 50, fontSize: 18, color: '#ffffff', fontWeight: 'normal', textAlign: 'center' },
          { id: 'cta', text: content.cta_text, x: 50, y: 80, fontSize: 16, color: '#ffffff', fontWeight: 'bold', textAlign: 'center' }
        );
    }
    return baseElements.filter(el => el.text && el.text.trim() !== '');
  };

  const getBackgroundStyle = () => {
    if (content.visual_style_suggestion?.background) {
      const bg = content.visual_style_suggestion.background;
      if (bg.style === 'gradient' && bg.colors) {
        return `linear-gradient(135deg, ${bg.colors[0]} 0%, ${bg.colors[1]} 100%)`;
      }
    }
    
    const backgrounds = {
      offer: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      announcement: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      product: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      informative: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      testimonial: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
    };
    return backgrounds[content.post_type.toLowerCase() as keyof typeof backgrounds] || backgrounds.informative;
  };

  useEffect(() => {
    const elements = getInitialTextPlacement(content);
    setTextElements(elements);
    setOriginalElements([...elements]); // Deep copy for reset
  }, [content]);

  const handleElementClick = (elementId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isEditModalOpen) return;
    setSelectedElement(elementId);
  };

  const handleMouseDown = (e: React.MouseEvent, elementId: string) => {
    if (!isEditModalOpen) return;
    e.preventDefault();
    
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    const element = textElements.find(el => el.id === elementId);
    if (!element) return;
    
    const elementX = (element.x / 100) * rect.width;
    const elementY = (element.y / 100) * rect.height;
    
    setDraggedElement(elementId);
    setDragOffset({
      x: e.clientX - rect.left - elementX,
      y: e.clientY - rect.top - elementY
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!draggedElement || !canvasRef.current || !isEditModalOpen) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const newX = ((e.clientX - rect.left - dragOffset.x) / rect.width) * 100;
    const newY = ((e.clientY - rect.top - dragOffset.y) / rect.height) * 100;
    
    const clampedX = Math.max(5, Math.min(95, newX));
    const clampedY = Math.max(5, Math.min(95, newY));
    
    setTextElements(prev => prev.map(el => 
      el.id === draggedElement ? { ...el, x: clampedX, y: clampedY } : el
    ));
  };

  const handleMouseUp = () => {
    setDraggedElement(null);
    setDragOffset({ x: 0, y: 0 });
  };

  const updateElementProperty = (elementId: string, property: keyof TextElement, value: any) => {
    setTextElements(prev => prev.map(el => 
      el.id === elementId ? { ...el, [property]: value } : el
    ));
  };

  const revertToOriginal = () => {
    setTextElements([...originalElements]);
    setSelectedElement(null);
  };

  const downloadImage = async () => {
    if (canvasRef.current) {
      const editControls = canvasRef.current.querySelectorAll('.edit-control');
      editControls.forEach(el => (el as HTMLElement).style.display = 'none');

      const canvas = await html2canvas(canvasRef.current, {
        useCORS: true,
        scale: 3, // Higher quality
        backgroundColor: null,
        allowTaint: true,
        foreignObjectRendering: true,
        logging: false,
        width: canvasRef.current.offsetWidth,
        height: canvasRef.current.offsetHeight,
      });

      editControls.forEach(el => (el as HTMLElement).style.display = 'block');

      const image = canvas.toDataURL('image/png', 1.0); // Maximum quality
      const link = document.createElement('a');
      link.href = image;
      link.download = `${businessName}-${content.post_type}-post.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const selectedElementData = selectedElement ? textElements.find(el => el.id === selectedElement) : null;

  return (
    <>
      {/* Main Post Card */}
      <Card className="w-full max-w-lg mx-auto shadow-lg rounded-xl overflow-hidden bg-white">
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">{content.post_type} Post</h3>
            <div className="flex gap-2">
              <Button
                onClick={() => setIsEditModalOpen(true)}
                size="sm"
                variant="outline"
              >
                <Edit3 className="h-4 w-4" />
                Edit
              </Button>
              <Button onClick={downloadImage} size="sm">
                <Download className="h-4 w-4" />
                Download
              </Button>
            </div>
          </div>

          <div
            ref={canvasRef}
            className="relative w-full aspect-square rounded-lg overflow-hidden min-h-[400px]"
            style={{ background: getBackgroundStyle() }}
          >
            {textElements.map((element) => (
              <div
                key={element.id}
                className={`absolute select-none pointer-events-none
                  ${element.id === 'cta' ? 'px-4 py-2 rounded-lg bg-white bg-opacity-90' : 'px-2 py-1'}
                `}
                style={{
                  left: `${element.x}%`,
                  top: `${element.y}%`,
                  transform: 'translate(-50%, -50%)',
                  fontSize: `${element.fontSize}px`,
                  color: element.color,
                  fontWeight: element.fontWeight,
                  textAlign: element.textAlign as any,
                  fontFamily: element.fontFamily || 'inherit',
                  textShadow: element.id !== 'cta' ? '2px 2px 4px rgba(0,0,0,0.7)' : 'none',
                  backgroundColor: element.id === 'cta' ? 'rgba(255,255,255,0.9)' : 'transparent',
                  borderRadius: element.id === 'cta' ? '8px' : '0',
                  maxWidth: '80%',
                  wordWrap: 'break-word',
                  lineHeight: '1.3',
                  whiteSpace: 'pre-wrap',
                  zIndex: element.id === 'cta' ? 10 : 5,
                  minHeight: `${element.fontSize * 1.3}px`, // Ensure minimum height
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {element.text}
              </div>
            ))}
          </div>

          <div className="mt-4 text-sm text-gray-600">
            <p><strong>Caption:</strong> {content.caption}</p>
            <p><strong>Hashtags:</strong> {content.hashtags.join(' ')}</p>
          </div>

          {/* Large Download Button */}
          <div className="mt-4 flex justify-center">
            <Button
              onClick={downloadImage}
              className="w-full max-w-xs"
              size="lg"
            >
              <Download className="h-5 w-5 mr-2" />
              Download High Quality Image
            </Button>
          </div>
        </div>
      </Card>

      {/* Edit Modal */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-xl font-semibold">Edit {content.post_type} Post</h2>
              <div className="flex gap-2">
                <Button onClick={revertToOriginal} variant="outline" size="sm">
                  <Undo2 className="h-4 w-4 mr-1" />
                  Reset
                </Button>
                <Button
                  onClick={() => setIsEditModalOpen(false)}
                  variant="outline"
                  size="sm"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="flex gap-6">
                {/* Large Canvas */}
                <div className="flex-1">
                  <div
                    className="relative w-full aspect-square rounded-lg overflow-hidden cursor-pointer border-2 border-gray-200"
                    style={{ background: getBackgroundStyle(), maxWidth: '500px' }}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseUp}
                    onClick={() => setSelectedElement(null)}
                  >
                    {textElements.map((element) => (
                      <div
                        key={element.id}
                        className={`absolute select-none cursor-pointer
                          hover:ring-2 hover:ring-blue-300
                          ${selectedElement === element.id ? 'ring-2 ring-blue-500' : ''}
                          ${element.id === 'cta' ? 'px-4 py-2 rounded-lg bg-white bg-opacity-90' : 'px-2 py-1'}
                        `}
                        style={{
                          left: `${element.x}%`,
                          top: `${element.y}%`,
                          transform: 'translate(-50%, -50%)',
                          fontSize: `${element.fontSize}px`,
                          color: element.color,
                          fontWeight: element.fontWeight,
                          textAlign: element.textAlign as any,
                          fontFamily: element.fontFamily || 'inherit',
                          textShadow: element.id !== 'cta' ? '2px 2px 4px rgba(0,0,0,0.7)' : 'none',
                          backgroundColor: element.id === 'cta' ? 'rgba(255,255,255,0.9)' : 'transparent',
                          borderRadius: element.id === 'cta' ? '8px' : '0',
                          maxWidth: '85%',
                          wordWrap: 'break-word',
                          lineHeight: '1.2',
                          whiteSpace: 'pre-wrap',
                          zIndex: element.id === 'cta' ? 10 : 5,
                        }}
                        onClick={(e) => handleElementClick(element.id, e)}
                        onMouseDown={(e) => handleMouseDown(e, element.id)}
                      >
                        {element.text}
                        {selectedElement === element.id && (
                          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
                            Drag to move
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Properties Panel */}
                <div className="w-80 bg-gray-50 rounded-lg p-4 space-y-4">
                  <div className="flex items-center gap-2 text-lg font-semibold">
                    <Type className="h-5 w-5" />
                    Text Properties
                  </div>

                  {selectedElementData ? (
                    <div className="space-y-4">
                      <div className="p-3 bg-white rounded border">
                        <h4 className="font-medium mb-3 text-blue-600">
                          Editing: {selectedElementData.id.toUpperCase()}
                        </h4>
                        
                        <div className="space-y-3">
                          <div>
                            <label className="text-sm font-medium">Text Content</label>
                            <textarea
                              value={selectedElementData.text}
                              onChange={(e) => updateElementProperty(selectedElementData.id, 'text', e.target.value)}
                              className="w-full px-3 py-2 border rounded resize-none mt-1"
                              rows={3}
                            />
                          </div>

                          <div>
                            <label className="text-sm font-medium">Font Size</label>
                            <div className="flex items-center gap-2 mt-1">
                              <input
                                type="range"
                                min="12"
                                max="60"
                                value={selectedElementData.fontSize}
                                onChange={(e) => updateElementProperty(selectedElementData.id, 'fontSize', parseInt(e.target.value))}
                                className="flex-1"
                              />
                              <span className="text-sm w-12">{selectedElementData.fontSize}px</span>
                            </div>
                          </div>

                          <div>
                            <label className="text-sm font-medium">Color</label>
                            <div className="flex items-center gap-2 mt-1">
                              <input
                                type="color"
                                value={selectedElementData.color}
                                onChange={(e) => updateElementProperty(selectedElementData.id, 'color', e.target.value)}
                                className="w-12 h-8 border rounded cursor-pointer"
                              />
                              <Input
                                value={selectedElementData.color}
                                onChange={(e) => updateElementProperty(selectedElementData.id, 'color', e.target.value)}
                                className="flex-1 text-sm"
                              />
                            </div>
                          </div>

                          <div>
                            <label className="text-sm font-medium">Font Weight</label>
                            <Select
                              value={selectedElementData.fontWeight}
                              onValueChange={(value) => updateElementProperty(selectedElementData.id, 'fontWeight', value)}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="normal">Normal</SelectItem>
                                <SelectItem value="bold">Bold</SelectItem>
                                <SelectItem value="lighter">Light</SelectItem>
                                <SelectItem value="bolder">Extra Bold</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <label className="text-sm font-medium">Alignment</label>
                            <Select
                              value={selectedElementData.textAlign}
                              onValueChange={(value) => updateElementProperty(selectedElementData.id, 'textAlign', value)}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="left">Left</SelectItem>
                                <SelectItem value="center">Center</SelectItem>
                                <SelectItem value="right">Right</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div>
                            <label className="text-sm font-medium">Position</label>
                            <div className="grid grid-cols-2 gap-2 mt-1">
                              <div>
                                <label className="text-xs text-gray-500">X (%)</label>
                                <Input
                                  type="number"
                                  min="0"
                                  max="100"
                                  value={Math.round(selectedElementData.x || 0)}
                                  onChange={(e) => updateElementProperty(selectedElementData.id, 'x', parseInt(e.target.value) || 0)}
                                  className="text-sm"
                                />
                              </div>
                              <div>
                                <label className="text-xs text-gray-500">Y (%)</label>
                                <Input
                                  type="number"
                                  min="0"
                                  max="100"
                                  value={Math.round(selectedElementData.y || 0)}
                                  onChange={(e) => updateElementProperty(selectedElementData.id, 'y', parseInt(e.target.value) || 0)}
                                  className="text-sm"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 py-8">
                      <Type className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>Select a text element to edit</p>
                      <p className="text-sm mt-1">Click on any text in the canvas</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}


