import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { VisualStyleSuggestion } from '@/lib/store'; // Import the new type

interface CarouselSlide {
  id: string;
  text: string;
}

interface CarouselCardProps {
  contentIdea: CarouselSlide[];
  textOverlays: string[];
  caption: string;
  hashtagSuggestions: string[];
  scheduledDatetime: string;
  callToAction: string;
  visualStyleSuggestion?: VisualStyleSuggestion; // New prop
}

// Helper function to get dynamic styles for text elements
const getTextStyle = (element?: { font?: { family?: string; size?: number; weight?: string; color?: string; } }): React.CSSProperties => {
  if (!element?.font) return {};
  return {
    fontFamily: element.font.family || 'Inter, sans-serif',
    fontSize: element.font.size ? `${element.font.size}px` : undefined,
    fontWeight: element.font.weight || undefined,
    color: element.font.color || undefined,
  };
};

// Helper function to get dynamic styles for positioning
const getPositionClasses = (position?: string) => {
  switch (position) {
    case 'top-left': return 'absolute top-4 left-4';
    case 'top-center': return 'absolute top-4 left-1/2 -translate-x-1/2';
    case 'top-right': return 'absolute top-4 right-4';
    case 'center': return 'absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2';
    case 'center-left': return 'absolute top-1/2 left-4 -translate-y-1/2';
    case 'center-right': return 'absolute top-1/2 right-4 -translate-y-1/2';
    case 'bottom-left': return 'absolute bottom-4 left-4';
    case 'bottom-center': return 'absolute bottom-4 left-1/2 -translate-x-1/2';
    case 'bottom-right': return 'absolute bottom-4 right-4';
    case 'below-heading': return 'mt-2';
    case 'below-testimonial': return 'mt-2';
    case 'below-offer-details': return 'mt-2';
    default: return '';
  }
};

// Helper function to get background styles
const getBackgroundStyle = (background?: { style?: string; colors?: string[]; color?: string; image_src?: string; pattern?: string }): React.CSSProperties => {
  if (!background) return {};
  if (background.style === 'gradient' && background.colors && background.colors.length >= 2) {
    return {
      background: `linear-gradient(to right, ${background.colors[0]}, ${background.colors[1]})`,
    };
  }
  if (background.style === 'solid' && background.color) {
    return { backgroundColor: background.color };
  }
  if (background.style === 'image' && background.image_src) {
    return {
      backgroundImage: `url(https://placehold.co/600x400/${background.image_src.split('.')[0].replace('#', '')}/ffffff?text=BG)`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    };
  }
  if (background.style === 'pattern') {
      return { backgroundImage: `url(https://www.transparenttextures.com/patterns/${background.pattern || 'diagmonds'}.png)`, backgroundSize: 'repeat' };
  }
  return {};
};

export function CarouselCard({
  contentIdea,
  textOverlays,
  caption,
  hashtagSuggestions,
  scheduledDatetime,
  callToAction,
  visualStyleSuggestion, // Destructure new prop
}: CarouselCardProps) {
  const isInformative = visualStyleSuggestion?.tag === 'informative';
  const isProduct = visualStyleSuggestion?.tag === 'product';
  const isTestimonial = visualStyleSuggestion?.tag === 'testimonial';
  const isAnnouncement = visualStyleSuggestion?.tag === 'announcement';
  const isOffer = visualStyleSuggestion?.tag === 'offer';

  return (
    <Card className="w-full max-w-md mx-auto shadow-md rounded-xl overflow-hidden bg-white relative">
      <CardHeader className="bg-gradient-to-r from-orange-500 to-red-500 p-4 text-white">
        <CardTitle className="text-xl font-bold">Instagram Carousel</CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-4">
        <Carousel className="w-full">
          <CarouselContent>
            {contentIdea.map((slide, index) => (
              <CarouselItem key={slide.id} className="carousel-item relative" style={getBackgroundStyle(visualStyleSuggestion?.background)}>
                 {/* Render elements based on visualStyleSuggestion for each slide */}
                {isInformative && visualStyleSuggestion && (
                  <>
                    {visualStyleSuggestion.heading && (
                      <h3 className={`text-2xl font-bold ${getPositionClasses(visualStyleSuggestion.heading.position)}`} style={getTextStyle(visualStyleSuggestion.heading)}>
                        {visualStyleSuggestion.heading.text}
                      </h3>
                    )}
                    {visualStyleSuggestion.subheading && (
                      <p className={`text-lg ${getPositionClasses(visualStyleSuggestion.subheading.position)}`} style={getTextStyle(visualStyleSuggestion.subheading)}>
                        {visualStyleSuggestion.subheading.text}
                      </p>
                    )}
                    {visualStyleSuggestion.image && (
                      <img
                        src={`https://placehold.co/150x150/cccccc/ffffff?text=SlideImg`}
                        alt="Informative Visual"
                        className={`w-auto h-auto max-w-[70%] max-h-[70%] object-contain ${getPositionClasses(visualStyleSuggestion.image.position)}`}
                      />
                    )}
                     {visualStyleSuggestion.logo && (
                      <img
                        src={`https://placehold.co/40x40/cccccc/ffffff?text=Logo`}
                        alt="Brand Logo"
                        className={`w-10 h-10 rounded-full ${getPositionClasses(visualStyleSuggestion.logo.position)}`}
                      />
                    )}
                  </>
                )}
                {/* Add similar rendering logic for other types (Product, Testimonial, etc.) if needed for carousel slides */}
                {/* For simplicity, only rendering slide text and basic visual if no specific type is matched */}
                <p className="text-lg font-medium text-gray-700">{slide.text}</p>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>

        <div>
          <p className="text-sm font-medium text-gray-700 mb-1">Text Overlays:</p>
          <div className="flex flex-wrap gap-2">
            {textOverlays.map((overlay, index) => (
              <span key={index} className="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">
                {overlay}
              </span>
            ))}
          </div>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-700 mb-1">Caption:</p>
          <p className="text-base text-gray-800">{caption}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-700 mb-1">Hashtags:</p>
          <div className="flex flex-wrap gap-2">
            {hashtagSuggestions.map((tag, index) => (
              <span key={index} className="px-3 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">
                {tag}
              </span>
            ))}
          </div>
        </div>
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>Scheduled: {new Date(scheduledDatetime).toLocaleString()}</span>
          <span className="font-semibold text-orange-700">Action: {callToAction}</span>
        </div>
      </CardContent>
    </Card>
  );
}

