'use client';

import { CampaignForm } from '@/components/CampaignForm';
import { LoginForm } from '@/components/LoginForm';
import { HeaderComponent } from '@/components/HeaderComponent';
import { useAuth } from '@repo/auth';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';

export function AppContent() {
  const { isAuthenticated, isLoading, checkAuthStatus } = useAuth();

  // Check auth status on mount and when authentication state might change
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <HeaderComponent />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Welcome Section */}
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Create Your Instagram Campaign
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Generate engaging Instagram content with AI-powered suggestions.
              Fill in your business details and let our AI create compelling posts for your campaign.
            </p>
          </div>

          {/* Campaign Form */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <CampaignForm />
          </div>
        </div>
      </div>
    </div>
  );
}
