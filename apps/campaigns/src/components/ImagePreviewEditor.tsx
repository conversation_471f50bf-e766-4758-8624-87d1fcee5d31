'use client';

import React, { useState, useRef, useEffect } from 'react';

const CANVAS_SIZE = 400;

interface TextOverlay {
  id: string;
  text: string;
  x: number;
  y: number;
  fontSize: number;
  color: string;
  fontFamily: string;
}

interface ImagePreviewEditorProps {
  initialContentIdea: string;
  initialTextOverlays: TextOverlay[];
}

export function ImagePreviewEditor({ initialContentIdea, initialTextOverlays }: ImagePreviewEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [ctx, setCtx] = useState<CanvasRenderingContext2D | null>(null);
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>(initialTextOverlays);
  const [selectedOverlay, setSelectedOverlay] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isLoading, setIsLoading] = useState(false);

  // Initialize canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas) {
      const context = canvas.getContext('2d');
      if (context) {
        setCtx(context);
        
        // Draw immediate gradient background
        const gradient = context.createLinearGradient(0, 0, CANVAS_SIZE, CANVAS_SIZE);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        context.fillStyle = gradient;
        context.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);
      }
    }
  }, []);

  // Add default text overlay if none exist
  useEffect(() => {
    if (textOverlays.length === 0 && initialContentIdea) {
      const defaultOverlay: TextOverlay = {
        id: 'default-1',
        text: initialContentIdea,
        x: CANVAS_SIZE / 2,
        y: CANVAS_SIZE / 2,
        fontSize: 24,
        color: '#ffffff',
        fontFamily: 'Arial'
      };
      setTextOverlays([defaultOverlay]);
    }
  }, [initialContentIdea, textOverlays.length]);

  // Redraw canvas when overlays change
  useEffect(() => {
    if (ctx) {
      drawCanvas();
    }
  }, [ctx, textOverlays, selectedOverlay]);

  const drawCanvas = () => {
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);

    // Draw gradient background
    const gradient = ctx.createLinearGradient(0, 0, CANVAS_SIZE, CANVAS_SIZE);
    gradient.addColorStop(0, '#667eea');
    gradient.addColorStop(1, '#764ba2');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, CANVAS_SIZE, CANVAS_SIZE);

    // Draw text overlays
    textOverlays.forEach((overlay) => {
      ctx.font = `${overlay.fontSize}px ${overlay.fontFamily}`;
      ctx.fillStyle = overlay.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // Add text shadow for better visibility
      ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
      ctx.shadowBlur = 4;
      ctx.shadowOffsetX = 2;
      ctx.shadowOffsetY = 2;
      
      ctx.fillText(overlay.text, overlay.x, overlay.y);
      
      // Reset shadow
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      // Draw selection border if selected
      if (selectedOverlay === overlay.id) {
        ctx.strokeStyle = '#3b82f6';
        ctx.lineWidth = 2;
        const textMetrics = ctx.measureText(overlay.text);
        const textWidth = textMetrics.width;
        const textHeight = overlay.fontSize;
        
        ctx.strokeRect(
          overlay.x - textWidth / 2 - 5,
          overlay.y - textHeight / 2 - 5,
          textWidth + 10,
          textHeight + 10
        );
      }
    });
  };

  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas || !ctx) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Check if click is on any text overlay
    let clickedOverlay = null;
    for (const overlay of textOverlays) {
      ctx.font = `${overlay.fontSize}px ${overlay.fontFamily}`;
      const textMetrics = ctx.measureText(overlay.text);
      const textWidth = textMetrics.width;
      const textHeight = overlay.fontSize;

      if (
        x >= overlay.x - textWidth / 2 &&
        x <= overlay.x + textWidth / 2 &&
        y >= overlay.y - textHeight / 2 &&
        y <= overlay.y + textHeight / 2
      ) {
        clickedOverlay = overlay.id;
        break;
      }
    }

    setSelectedOverlay(clickedOverlay);
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!selectedOverlay) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const overlay = textOverlays.find(o => o.id === selectedOverlay);
    if (overlay) {
      setIsDragging(true);
      setDragOffset({
        x: x - overlay.x,
        y: y - overlay.y
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging || !selectedOverlay) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setTextOverlays(prev => prev.map(overlay => 
      overlay.id === selectedOverlay
        ? { ...overlay, x: x - dragOffset.x, y: y - dragOffset.y }
        : overlay
    ));
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const addTextOverlay = () => {
    const newOverlay: TextOverlay = {
      id: `overlay-${Date.now()}`,
      text: 'New Text',
      x: CANVAS_SIZE / 2,
      y: CANVAS_SIZE / 2,
      fontSize: 24,
      color: '#ffffff',
      fontFamily: 'Arial'
    };
    setTextOverlays(prev => [...prev, newOverlay]);
    setSelectedOverlay(newOverlay.id);
  };

  const updateSelectedOverlay = (updates: Partial<TextOverlay>) => {
    if (!selectedOverlay) return;

    setTextOverlays(prev => prev.map(overlay =>
      overlay.id === selectedOverlay
        ? { ...overlay, ...updates }
        : overlay
    ));
  };

  const deleteSelectedOverlay = () => {
    if (!selectedOverlay) return;

    setTextOverlays(prev => prev.filter(overlay => overlay.id !== selectedOverlay));
    setSelectedOverlay(null);
  };

  const downloadImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = 'post-image.png';
    link.href = canvas.toDataURL();
    link.click();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p>Loading image...</p>
        </div>
      </div>
    );
  }

  const selectedOverlayData = textOverlays.find(o => o.id === selectedOverlay);

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Post Preview (Drag & Drop Text)</h3>
        <div className="inline-block border border-gray-300 rounded-lg">
          <canvas
            ref={canvasRef}
            width={CANVAS_SIZE}
            height={CANVAS_SIZE}
            className="cursor-pointer rounded-lg"
            onClick={handleCanvasClick}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            style={{ display: 'block', width: CANVAS_SIZE, height: CANVAS_SIZE }}
          />
        </div>
        <p className="text-sm text-gray-500 mt-2">
          * Drag the text elements on the preview to reposition them.
        </p>
      </div>

      <div className="flex justify-center space-x-2">
        <button
          onClick={addTextOverlay}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Add Text
        </button>
        {selectedOverlay && (
          <button
            onClick={deleteSelectedOverlay}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Delete Selected
          </button>
        )}
        <button
          onClick={downloadImage}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          Download Post (PNG)
        </button>
      </div>

      {selectedOverlayData && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2">Edit Selected Text</h4>
          <div className="space-y-2">
            <div>
              <label className="block text-sm font-medium mb-1">Text</label>
              <input
                type="text"
                value={selectedOverlayData.text}
                onChange={(e) => updateSelectedOverlay({ text: e.target.value })}
                className="w-full px-3 py-1 border border-gray-300 rounded"
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-sm font-medium mb-1">Font Size</label>
                <input
                  type="number"
                  value={selectedOverlayData.fontSize}
                  onChange={(e) => updateSelectedOverlay({ fontSize: parseInt(e.target.value) })}
                  className="w-full px-3 py-1 border border-gray-300 rounded"
                  min="12"
                  max="72"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Color</label>
                <input
                  type="color"
                  value={selectedOverlayData.color}
                  onChange={(e) => updateSelectedOverlay({ color: e.target.value })}
                  className="w-full h-8 border border-gray-300 rounded"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}


