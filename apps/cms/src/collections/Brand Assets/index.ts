import { CollectionConfig } from 'payload';

export const BrandAssets: CollectionConfig = {
  slug: 'brandAssets',
  admin: {
    group: 'Branding',
  },
  fields: [
    {
      name: 'brandColors',
      type: 'group',
      fields: [
        { name: 'primary', type: 'text' },
        { name: 'secondary', type: 'text' },
        { name: 'accent', type: 'text' },
      ],
    },
    {
      name: 'brandFonts',
      type: 'group',
      fields: [
        { name: 'primary', type: 'text' },
        { name: 'secondary', type: 'text' },
      ],
    },
    {
      name: 'graphicAssets',
      type: 'group',
      fields: [
        { name: 'useGradients', type: 'checkbox' },
        { name: 'useGlowEffects', type: 'checkbox' },
        { name: 'useThickBorders', type: 'checkbox' },
      ],
    },
    {
      name: 'productInfo',
      type: 'group',
      fields: [
        { name: 'name', type: 'text' },
        { name: 'price', type: 'text' },
        {
          name: 'variants',
          type: 'array',
          fields: [{ name: 'variant', type: 'text' }],
        },
        { name: 'description', type: 'textarea' },
      ],
    },
  ],
};