# Horizontal Header Component Implementation

## Overview

This implementation adds a horizontal header component to the brands app that integrates with PayloadCMS for authentication and database operations. The header provides brand and brand asset management functionality with two distinct modes: Create Mode and Edit Mode.

## Architecture

### Data Flow
- **Users** → **Brands** → **Brand Assets** (PayloadCMS relationship hierarchy)
- **Type A Mode (Create)**: Uses SSE server + user input → Creates new brand asset
- **Edit Mode**: Fetches from PayloadCMS database → Updates existing brand asset

### Key Components

#### 1. PayloadCMS Integration (`/src/lib/payload-client.ts`)
- REST API client for PayloadCMS backend
- JWT token management with localStorage persistence
- CRUD operations for users, brands, and brand assets
- Automatic token refresh and error handling

#### 2. Authentication System
- **`/src/hooks/useAuth.ts`**: Authentication state management
- **`/src/components/AuthProvider.tsx`**: React context for auth state
- **`/src/components/LoginForm.tsx`**: Login interface
- Integration with Zustand store for global state

#### 3. API Operations (`/src/hooks/usePayloadAPI.ts`)
- Wrapper hooks for PayloadCMS operations
- Loading states and error handling
- Brand and brand asset CRUD operations

#### 4. Extended State Management (`/src/store/brandingStore.ts`)
- Added authentication state
- Mode management (create vs edit)
- Brand/asset selection state
- Database operation states
- Seamless integration with existing branding data

#### 5. Header Component (`/src/components/HeaderComponent.tsx`)
- **Left Side**: Brand selector, Brand asset selector, Mode badge
- **Right Side**: User info, Save/Update button, Logout button
- Conditional rendering based on authentication and mode
- Error handling and loading states

## Features

### Authentication
- PayloadCMS JWT-based authentication
- Persistent login sessions
- Automatic token refresh
- Secure logout with state cleanup

### Brand Management
- Dropdown to select from user's associated brands
- Dynamic loading of brand assets for selected brand
- "Create New Asset" option to switch to Type A mode

### Mode System

#### Type A Mode (Create New)
- Triggered when "Create New Asset" is selected
- Uses existing SSE server for real-time AI generation
- Form data from user input and SSE responses
- "Save" button creates new brand asset in PayloadCMS
- Associates new asset with selected brand

#### Edit Mode
- Triggered when existing brand asset is selected
- Fetches complete data from PayloadCMS database
- Disables SSE connections (no AI generation)
- Populates all form fields with database data
- "Update" button saves changes to existing record

### UI/UX Features
- Shadcn UI components for consistent design
- Loading states for all async operations
- Error handling with user-friendly messages
- Responsive layout adjustments
- Mode-aware status indicators

## File Structure

```
apps/brands/src/
├── components/
│   ├── AuthProvider.tsx          # Authentication context provider
│   ├── HeaderComponent.tsx       # Main header component
│   ├── LoginForm.tsx            # Login interface
│   └── BrandingDataSection.tsx  # Updated with mode awareness
├── hooks/
│   ├── useAuth.ts               # Authentication hook
│   └── usePayloadAPI.ts         # API operations hook
├── lib/
│   └── payload-client.ts        # PayloadCMS client
├── store/
│   └── brandingStore.ts         # Extended Zustand store
└── app/
    └── page.tsx                 # Updated main page layout
```

## Integration Points

### With Existing Components
- **BrandingDataSection**: Updated to show mode-aware status
- **InputSection**: Only visible in Create Mode
- **VisualPreviewSection**: Full width in Edit Mode
- **SSE System**: Disabled in Edit Mode, active in Create Mode

### With PayloadCMS Backend
- Uses existing collections: users, brands, brandAssets
- Respects existing relationship structure
- Maintains data integrity and access controls

## Usage

### For Users
1. Login with PayloadCMS credentials
2. Select a brand from the dropdown
3. Choose "Create New Asset" or select existing asset
4. In Create Mode: Use AI generation via SSE
5. In Edit Mode: Modify existing data manually
6. Save/Update changes to PayloadCMS database

### For Developers
1. Ensure PayloadCMS backend is running on localhost:3000
2. User accounts must have associated brands
3. Brands can have multiple brand assets
4. All operations respect PayloadCMS access controls

## Error Handling
- Network errors with retry mechanisms
- Authentication failures with re-login prompts
- Validation errors with user feedback
- Graceful degradation for missing data

## Security
- JWT tokens stored securely in localStorage
- Automatic token refresh before expiration
- Secure logout with complete state cleanup
- All API calls include proper authentication headers

## Future Enhancements
- Bulk operations for brand assets
- Advanced filtering and search
- Real-time collaboration features
- Audit logging for changes
- Export/import functionality
