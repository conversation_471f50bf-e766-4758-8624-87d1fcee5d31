import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Custom React hook for managing SSE connection to Flask server
 */
export function useBrandSSE(serverUrl = 'http://localhost:5001') {
  const [brandData, setBrandData] = useState<object | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [events, setEvents] = useState<string[]>([]);

  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const baseReconnectDelay = 1000; // 1 second

  const connect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    setConnectionStatus('connecting');
    setError(null);

    try {
      const eventSource = new EventSource(`${serverUrl}/events`);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('SSE connection opened');
        setConnectionStatus('connected');
        setError(null);
        reconnectAttempts.current = 0;
      };

      eventSource.onmessage = (event) => {
        try {
          setEvents(prev => [...prev, `${new Date().toLocaleTimeString()} - ${event.data}`]);
        } catch (err) {
          console.error('Error receiving SSE data:', err);
          setError('Failed to receive SSE data');
        }
      };

      // Handle custom event types
      eventSource.addEventListener('brand-update', (event) => {
        try {
          const data = JSON.parse(event.data);
          setBrandData(data);
          setLastUpdated(new Date());
          console.log('Brand data updated:', data);
        } catch (err) {
          console.error('Error parsing brand update:', err);
        }
      });

      eventSource.addEventListener('initial-data', (event) => {
        try {
          const data = JSON.parse(event.data);
          setBrandData(data);
          setLastUpdated(new Date());
          console.log('Initial brand data received:', data);
        } catch (err) {
          console.error('Error parsing initial data:', err);
        }
      });

      eventSource.onerror = (event) => {
        console.error('SSE connection error:', event);
        setConnectionStatus('error');
        
        if (eventSource.readyState === EventSource.CLOSED) {
          setConnectionStatus('disconnected');
          
          // Attempt reconnection with exponential backoff
          if (reconnectAttempts.current < maxReconnectAttempts) {
            const delay = baseReconnectDelay * Math.pow(2, reconnectAttempts.current);
            reconnectAttempts.current++;
            
            setError(`Connection lost. Reconnecting in ${delay/1000}s... (Attempt ${reconnectAttempts.current}/${maxReconnectAttempts})`);
            
            reconnectTimeoutRef.current = setTimeout(() => {
              connect();
            }, delay);
          } else {
            setError('Connection failed after maximum retry attempts. Please refresh the page.');
          }
        }
      };

    } catch (err) {
      console.error('Failed to create EventSource:', err);
      setConnectionStatus('error');
      setError('Failed to establish connection');
    }
  }, [serverUrl]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setConnectionStatus('disconnected');
    reconnectAttempts.current = 0;
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 100);
  }, [connect, disconnect]);

  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    brandData,
    connectionStatus,
    error,
    lastUpdated,
    events,
    isConnected: connectionStatus === 'connected',
    reconnect,
    disconnect
  };
}
