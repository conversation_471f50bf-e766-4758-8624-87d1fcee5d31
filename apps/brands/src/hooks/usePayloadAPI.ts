'use client';

import { useState, useCallback } from 'react';
import { Brand, BrandAsset, payloadClient } from '@repo/auth';

interface APIState {
  isLoading: boolean;
  error: string | null;
}

export function usePayloadAPI() {
  const [apiState, setApiState] = useState<APIState>({
    isLoading: false,
    error: null,
  });

  const setLoading = useCallback((loading: boolean) => {
    setApiState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setApiState(prev => ({ ...prev, error }));
  }, []);

  const clearError = useCallback(() => {
    setApiState(prev => ({ ...prev, error: null }));
  }, []);

  // Brand operations
  const fetchBrandsByUser = useCallback(async (userId: number): Promise<Brand[]> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await payloadClient.getBrandsByUser(userId);
      return result.docs || [];
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch brands';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  const createBrand = useCallback(async (brandData: Partial<Brand>): Promise<Brand> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await payloadClient.createBrand(brandData);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create brand';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  const updateBrand = useCallback(async (brandId: number, brandData: Partial<Brand>): Promise<Brand> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await payloadClient.updateBrand(brandId, brandData);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update brand';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  const fetchBrand = useCallback(async (brandId: number): Promise<Brand> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await payloadClient.getBrand(brandId);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch brand';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  // Brand Asset operations
  const fetchBrandAssets = useCallback(async (brandId: number): Promise<BrandAsset[]> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await payloadClient.getBrandAssets(brandId);
      return result.docs || [];
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch brand assets';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  const fetchBrandAsset = useCallback(async (assetId: number): Promise<BrandAsset> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await payloadClient.getBrandAsset(assetId);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch brand asset';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  const createBrandAsset = useCallback(async (assetData: Partial<BrandAsset>): Promise<BrandAsset> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await payloadClient.createBrandAsset(assetData);
      return result.doc;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create brand asset';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  const updateBrandAsset = useCallback(async (assetId: number, assetData: Partial<BrandAsset>): Promise<BrandAsset> => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await payloadClient.updateBrandAsset(assetId, assetData);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update brand asset';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  const deleteBrandAsset = useCallback(async (assetId: number): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      await payloadClient.deleteBrandAsset(assetId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete brand asset';
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [setLoading, setError]);

  return {
    ...apiState,
    clearError,
    // Brand operations
    fetchBrandsByUser,
    createBrand,
    updateBrand,
    fetchBrand,
    // Brand Asset operations
    fetchBrandAssets,
    fetchBrandAsset,
    createBrandAsset,
    updateBrandAsset,
    deleteBrandAsset,
  };
}
