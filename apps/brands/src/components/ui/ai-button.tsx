import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const aiButtonVariants = cva(
  "relative inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-offset-2 overflow-hidden group",
  {
    variants: {
      variant: {
        ai: "bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-500 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] focus-visible:ring-purple-500 before:absolute before:inset-0 before:bg-gradient-to-r before:from-purple-400 before:via-blue-400 before:to-cyan-400 before:opacity-0 before:transition-opacity before:duration-300 hover:before:opacity-20",
        aiSecondary: "bg-gradient-to-r from-slate-800 via-purple-900 to-slate-800 text-white shadow-lg hover:shadow-xl hover:scale-[1.02] focus-visible:ring-purple-500 border border-purple-500/30 hover:border-purple-400/50",
      },
      size: {
        default: "h-10 px-6 py-2",
        sm: "h-8 rounded-md gap-1.5 px-4",
        lg: "h-12 rounded-lg px-8 text-base",
        icon: "size-10",
      },
    },
    defaultVariants: {
      variant: "ai",
      size: "default",
    },
  }
)

interface SparkleProps {
  className?: string;
}

const Sparkle: React.FC<SparkleProps> = ({ className }) => (
  <svg
    className={cn("absolute animate-pulse", className)}
    width="6"
    height="6"
    viewBox="0 0 6 6"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3 0L3.5 2.5L6 3L3.5 3.5L3 6L2.5 3.5L0 3L2.5 2.5L3 0Z"
      fill="currentColor"
    />
  </svg>
)

interface AIButtonProps extends React.ComponentProps<"button">, VariantProps<typeof aiButtonVariants> {
  asChild?: boolean;
  showSparkles?: boolean;
}

const AIButton = React.forwardRef<HTMLButtonElement, AIButtonProps>(
  ({ className, variant, size, asChild = false, showSparkles = true, children, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"

    return (
      <Comp
        className={cn(aiButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {showSparkles && (
          <>
            <Sparkle className="top-1 left-2 text-white/60 animate-pulse delay-0" />
            <Sparkle className="top-2 right-3 text-white/40 animate-pulse delay-300" />
            <Sparkle className="bottom-1 left-4 text-white/50 animate-pulse delay-700" />
            <Sparkle className="bottom-2 right-2 text-white/60 animate-pulse delay-1000" />
          </>
        )}
        <span className="relative z-10 flex items-center gap-2">
          {children}
        </span>
      </Comp>
    )
  }
)

AIButton.displayName = "AIButton"

export { AIButton, aiButtonVariants }
