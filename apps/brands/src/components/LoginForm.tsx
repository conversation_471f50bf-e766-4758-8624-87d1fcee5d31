'use client';

import React, { useState } from 'react';
import { LoginForm as SharedLoginForm } from '@repo/ui/login-form';
import { useAuthContext } from './AuthProvider';
import { useBrandingStore } from '@/store/brandingStore';

export function LoginForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login } = useAuthContext();
  const { authState } = useBrandingStore();

  const handleSubmit = async (email: string, password: string) => {
    setIsSubmitting(true);

    try {
      await login(email, password);
    } catch (error) {
      console.error('Login failed:', error);
      throw error; // Re-throw to let the shared component handle it
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SharedLoginForm
      onSubmit={handleSubmit}
      isLoading={isSubmitting}
      error={authState.error}
      title="Brand Management Login"
    />
  );
}
