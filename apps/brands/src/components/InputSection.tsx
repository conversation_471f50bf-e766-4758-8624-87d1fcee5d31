'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { AIButton } from '@/components/ui/ai-button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useBrandingStore } from '@/store/brandingStore';
import { Send, Loader2, Sparkles } from 'lucide-react';
import axios from 'axios'
import { useBrandSSE } from '@/hooks/useBrandSSE'; // <-- Add this import


export function InputSection() {
  const {
    businessInput,
    updateBusinessInput,
    updateDynamicFields,
    updateDynamicFieldValue
  } = useBrandingStore();

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingFields, setIsGeneratingFields] = useState(false);

  const handleGenerateFields = async () => {
    if (!businessInput.name || !businessInput.description) {
      return;
    }

    setIsGeneratingFields(true);

    try {
      const response = await axios.post('http://localhost:5001/generate-dynamic-fields', {
        name: businessInput.name,
        description: businessInput.description
      });

      if (response.data && response.data.fields) {
        updateDynamicFields(response.data.fields);
      }
    } catch (error) {
      console.error('Error generating business fields:', error);
    } finally {
      setIsGeneratingFields(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!businessInput.name || !businessInput.description || !businessInput.visualStyle) {
      return;
    }

    setIsSubmitting(true);

    const payload = {
      name: businessInput.name,
      description: businessInput.description,
      visualStyle: businessInput.visualStyle,
      dynamicFields: businessInput.dynamicFields.map(field => ({
        Field: field.Field,
        Value: field.value // assuming you captured this from user input
      }))
    };

    try {
      // Send HTTP POST request to your Python app
      await axios.post('http://localhost:5001/trigger-python-app', payload);

      // Simulate form submission
      setTimeout(() => {
        setIsSubmitting(false);
      }, 1000);
    } 
    catch (error) {
      console.error('Error triggering Python app:', error);
      setIsSubmitting(false);
    }
  };

  const isFormValid = businessInput.name && businessInput.description && businessInput.visualStyle;

  const { events = [], isConnected, error } = useBrandSSE(); // <-- Use the hook

  return (
    <div className="h-full p-6 overflow-y-auto">
      <Card className="border-0 shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Business Information
          </CardTitle>
          <p className="text-gray-600">
            Tell us about your business to generate your brand identity
          </p>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Business Name */}
            <div className="space-y-2">
              <Label htmlFor="businessName" className="text-sm font-medium text-gray-700">
                Business Name *
              </Label>
              <Input
                id="businessName"
                placeholder="Enter your business name"
                value={businessInput.name}
                onChange={(e) => updateBusinessInput({ name: e.target.value })}
                className="w-full"
                disabled={isSubmitting}
              />
            </div>

            {/* Business Description */}
            <div className="space-y-2">
              <Label htmlFor="businessDescription" className="text-sm font-medium text-gray-700">
                Business Description *
              </Label>
              <Textarea
                id="businessDescription"
                placeholder="Describe what your business does, your target audience, and key values..."
                value={businessInput.description}
                onChange={(e) => updateBusinessInput({ description: e.target.value })}
                className="w-full min-h-[120px] resize-none"
                disabled={isSubmitting}
              />
            </div>

            {/* Visual Style Preference */}
            <div className="space-y-2">
              <Label htmlFor="visualStyle" className="text-sm font-medium text-gray-700">
                Preferred Visual Style *
              </Label>
              <Textarea
                id="visualStyle"
                placeholder="Describe your preferred style"
                value={businessInput.visualStyle}
                onChange={(e) => updateBusinessInput({ visualStyle: e.target.value })}
                className="w-full min-h-[120px] resize-none"
                disabled={isSubmitting}
              />
            </div>

            {/* AI Generate Fields Button */}
            <div className="space-y-4">
              <AIButton
                type="button"
                onClick={handleGenerateFields}
                disabled={!businessInput.name || !businessInput.description || isGeneratingFields}
                size="lg"
                className="w-full"
              >
                {isGeneratingFields ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating Business Relevant Fields...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Generate Business Relevant Fields
                  </>
                )}
              </AIButton>
            </div>

            {/* Dynamic Fields */}
            {businessInput.dynamicFields.length > 0 && (
              <div className="space-y-4 animate-in slide-in-from-top-4 duration-500">
                <div className="border-t pt-4">
                  <Label className="text-sm font-medium text-gray-700 mb-3 block">
                    Business Specific Information
                  </Label>
                  <div className="space-y-4">
                    {businessInput.dynamicFields.map((field, index) => (
                      <div
                        key={index}
                        className="space-y-2 animate-in slide-in-from-left-4 duration-300"
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        <Label className="text-sm font-medium text-gray-600">
                          {field.Field}
                        </Label>
                        <Input
                          placeholder={field.Placeholder}
                          value={field.value || ''}
                          onChange={(e) => updateDynamicFieldValue(index, e.target.value)}
                          className="w-full transition-all duration-200 focus:ring-2 focus:ring-purple-500/20"
                          disabled={isSubmitting}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Live Events</Label>
              <div className="bg-gray-100 rounded p-2 h-32 overflow-y-auto text-xs font-mono">
                {!isConnected && <div className="text-gray-400">Connecting...</div>}
                {error && <div className="text-red-500">Error: {error}</div>}
                {events.length === 0 && isConnected && !error && (
                  <span className="text-gray-400">No events yet.</span>
                )}
                {events.slice(-5).reverse().map((e, i) => (
                  <div key={i}>{e}</div>
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full"
              disabled={!isFormValid || isSubmitting}
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Brand Identity...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Generate Brand Identity
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
