'use client';

import { InputSection } from '@/components/InputSection';
import { BrandingDataSection } from '@/components/BrandingDataSection';
import { VisualPreviewSection } from '@/components/VisualPreviewSection';
import { HeaderComponent } from '@/components/HeaderComponent';
import { LoginForm } from '@/components/LoginForm';
import { AuthProvider } from '@/components/AuthProvider';
import { useBrandingStore } from '@/store/brandingStore';

function AppContent() {
  const { authState, appMode } = useBrandingStore();

  // Show login form if not authenticated
  if (!authState.isAuthenticated) {
    return <LoginForm />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section - Brand Management */}
      <div className="w-full border-b bg-white">
        <HeaderComponent />
      </div>

      {/* Branding Data Section */}
      <div className="w-full border-b bg-white">
        <BrandingDataSection />
      </div>

      {/* Main Content - Left and Right Sections */}
      <div className="flex h-[calc(100vh-160px)]">
        {/* Left Section - Input (only show in create mode) */}
        {appMode === 'create' && (
          <div className="w-1/3 border-r bg-white">
            <InputSection />
          </div>
        )}

        {/* Right Section - Visual Preview */}
        <div className={`${appMode === 'create' ? 'flex-1' : 'w-full'} bg-gray-50`}>
          <VisualPreviewSection />
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}
