'use client'

import { useState, useEffect } from 'react'

interface BreakpointConfig {
  xs: number
  sm: number
  md: number
  lg: number
  xl: number
  '2xl': number
  '3xl': number
}

const breakpoints: BreakpointConfig = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
  '3xl': 1600,
}

export function useResponsive() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
  })
  
  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  const isBreakpoint = (breakpoint: keyof BreakpointConfig) => {
    return windowSize.width >= breakpoints[breakpoint]
  }
  
  const isMobile = windowSize.width < breakpoints.md
  const isTablet = windowSize.width >= breakpoints.md && windowSize.width < breakpoints.lg
  const isDesktop = windowSize.width >= breakpoints.lg
  
  const getCurrentBreakpoint = (): keyof BreakpointConfig => {
    if (windowSize.width >= breakpoints['3xl']) return '3xl'
    if (windowSize.width >= breakpoints['2xl']) return '2xl'
    if (windowSize.width >= breakpoints.xl) return 'xl'
    if (windowSize.width >= breakpoints.lg) return 'lg'
    if (windowSize.width >= breakpoints.md) return 'md'
    if (windowSize.width >= breakpoints.sm) return 'sm'
    return 'xs'
  }
  
  return {
    windowSize,
    isBreakpoint,
    isMobile,
    isTablet,
    isDesktop,
    currentBreakpoint: getCurrentBreakpoint(),
    breakpoints,
  }
}
