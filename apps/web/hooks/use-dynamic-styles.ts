'use client'

import { useEffect } from 'react'
import { useDesignStore } from '../store/design-store'
import { generateCSSProperties } from '../lib/css-generator'

export function useDynamicStyles() {
  const { parameters } = useDesignStore()
  
  useEffect(() => {
    const cssProperties = generateCSSProperties(parameters)
    const root = document.documentElement
    
    // Apply CSS custom properties to the root element
    Object.entries(cssProperties).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })
    
    // Cleanup function to remove properties when component unmounts
    return () => {
      Object.keys(cssProperties).forEach((property) => {
        root.style.removeProperty(property)
      })
    }
  }, [parameters])
  
  return parameters
}
