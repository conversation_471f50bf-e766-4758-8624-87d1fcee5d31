// Background Styling Types
export type BackgroundType = 
  | 'solid'
  | 'gradient-linear'
  | 'gradient-radial'
  | 'gradient-conic'
  | 'texture-subtle'
  | 'texture-paper'
  | 'texture-fabric'
  | 'pattern-geometric'
  | 'pattern-organic'
  | 'pattern-dots'
  | 'pattern-lines'
  | 'image-hero'
  | 'image-subtle'
  | 'mesh-gradient'
  | 'glass-morphism'

export type BackgroundIntensity = 'subtle' | 'moderate' | 'bold' | 'dramatic'
export type BackgroundMovement = 'static' | 'parallax' | 'floating' | 'animated-gradient' | 'particle-system'

// Card and Component Styling Types
export type CardStyle = 
  | 'flat'
  | 'elevated'
  | 'floating'
  | 'outlined'
  | 'glass'
  | 'neumorphism'
  | 'brutalist'
  | 'organic'
  | 'geometric'
  | 'gradient-border'
  | 'neon-glow'
  | 'paper'

export type CardRadius = 'none' | 'small' | 'medium' | 'large' | 'xl' | 'full'

export type CardShadow = 
  | 'none'
  | 'subtle'
  | 'medium'
  | 'large'
  | 'colored'
  | 'multi-layer'
  | 'inner'
  | 'glow'

// Button Design Types
export type ButtonStyle = 
  | 'solid'
  | 'outline'
  | 'ghost'
  | 'gradient'
  | 'glass'
  | 'neumorphic'
  | 'floating'
  | 'pill'
  | 'sharp'
  | 'split'
  | 'animated'
  | 'neon'

export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'

export type ButtonHoverEffect = 
  | 'scale'
  | 'lift'
  | 'glow'
  | 'slide'
  | 'rotate'
  | 'pulse'
  | 'ripple'
  | 'morph'
  | 'color-shift'
  | 'none'

// Animation Behavior Types
export type AnimationSpeed = 'instant' | 'fast' | 'normal' | 'slow' | 'slower' | 'slowest'

export type AnimationEasing = 
  | 'linear'
  | 'ease'
  | 'ease-in'
  | 'ease-out'
  | 'ease-in-out'
  | 'bounce'
  | 'elastic'
  | 'spring'
  | 'custom-cubic'

export type InteractionType = 
  | 'minimal'
  | 'standard'
  | 'playful'
  | 'sophisticated'
  | 'dramatic'
  | 'micro'
  | 'immersive'

// Typography Types
export interface FontPairing {
  heading: string
  body: string
  accent: string
}

export type TypographyScale = 'compact' | 'standard' | 'generous' | 'dramatic'
export type TypographySpacing = 'tight' | 'normal' | 'loose' | 'dramatic'
export type TypographyWeight = 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold'

// Color Application Types
export type ColorStrategy = 
  | 'monochromatic'
  | 'analogous'
  | 'complementary'
  | 'triadic'
  | 'split-complementary'
  | 'tetradic'
  | 'neutral-accent'
  | 'gradient-heavy'
  | 'high-contrast'
  | 'low-contrast'

export type ColorIntensity = 'muted' | 'balanced' | 'vibrant' | 'electric'

export type ColorDistribution = 
  | 'primary-dominant'
  | 'balanced'
  | 'accent-heavy'
  | 'neutral-base'
  | 'gradient-flow'

// Layout and Spacing Types
export type SpacingScale = 'tight' | 'normal' | 'loose' | 'generous'
export type LayoutDensity = 'compact' | 'balanced' | 'spacious' | 'minimal'

export type GridSystem = 
  | 'standard-12'
  | 'flexible'
  | 'masonry'
  | 'asymmetric'
  | 'modular'
  | 'fluid'

export type ContainerWidth = 'narrow' | 'standard' | 'wide' | 'full'

// Visual Effects Types
export interface GlassEffect {
  blur: 'none' | 'subtle' | 'medium' | 'strong'
  opacity: '10%' | '20%' | '30%' | '40%'
  border: 'none' | 'subtle' | 'visible'
  backdrop: 'blur' | 'saturate' | 'contrast'
}

export interface GlowEffect {
  intensity: 'subtle' | 'medium' | 'strong' | 'dramatic'
  color: 'brand' | 'accent' | 'white' | 'custom'
  spread: 'tight' | 'medium' | 'wide'
  animation: 'static' | 'pulse' | 'breathing'
}

export type ShadowComplexity =
  | 'simple'
  | 'layered'
  | 'realistic'
  | 'artistic'
  | 'colored'
  | 'dynamic'

// Main Design Parameters Interface
export interface DesignParameters {
  // Background Styling
  backgroundType: BackgroundType
  backgroundIntensity: BackgroundIntensity
  backgroundMovement: BackgroundMovement

  // Card and Component Styling
  cardStyle: CardStyle
  cardRadius: CardRadius
  cardShadow: CardShadow

  // Button Design
  buttonStyle: ButtonStyle
  buttonSize: ButtonSize
  buttonHoverEffect: ButtonHoverEffect

  // Animation Behavior
  animationSpeed: AnimationSpeed
  animationEasing: AnimationEasing
  interactionType: InteractionType

  // Typography Styling
  fontPairing: FontPairing
  typographyScale: TypographyScale
  typographySpacing: TypographySpacing
  typographyWeight: TypographyWeight

  // Color Application
  colorStrategy: ColorStrategy
  colorIntensity: ColorIntensity
  colorDistribution: ColorDistribution

  // Layout and Spacing
  spacingScale: SpacingScale
  layoutDensity: LayoutDensity
  gridSystem: GridSystem
  containerWidth: ContainerWidth

  // Visual Effects
  glassEffect: GlassEffect
  glowEffect: GlowEffect
  shadowComplexity: ShadowComplexity

  // Color Palette
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    foreground: string
    muted: string
    border: string
  }
}

// Default Design Parameters
export const defaultDesignParameters: DesignParameters = {
  backgroundType: 'gradient-linear',
  backgroundIntensity: 'moderate',
  backgroundMovement: 'static',

  cardStyle: 'elevated',
  cardRadius: 'medium',
  cardShadow: 'medium',

  buttonStyle: 'solid',
  buttonSize: 'md',
  buttonHoverEffect: 'lift',

  animationSpeed: 'normal',
  animationEasing: 'ease-out',
  interactionType: 'standard',

  fontPairing: {
    heading: 'Inter',
    body: 'Inter',
    accent: 'Inter'
  },
  typographyScale: 'standard',
  typographySpacing: 'normal',
  typographyWeight: 'normal',

  colorStrategy: 'complementary',
  colorIntensity: 'balanced',
  colorDistribution: 'primary-dominant',

  spacingScale: 'normal',
  layoutDensity: 'balanced',
  gridSystem: 'standard-12',
  containerWidth: 'standard',

  glassEffect: {
    blur: 'medium',
    opacity: '20%',
    border: 'subtle',
    backdrop: 'blur'
  },
  glowEffect: {
    intensity: 'medium',
    color: 'brand',
    spread: 'medium',
    animation: 'static'
  },
  shadowComplexity: 'simple',

  colors: {
    primary: '#3B82F6',
    secondary: '#1E40AF',
    accent: '#F59E0B',
    background: '#FFFFFF',
    foreground: '#171717',
    muted: '#F3F4F6',
    border: '#E5E7EB'
  }
}
