import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Utility function to convert design parameters to CSS custom properties
export function generateCSSVariables(params: Record<string, any>): Record<string, string> {
  const cssVars: Record<string, string> = {}
  
  Object.entries(params).forEach(([key, value]) => {
    if (typeof value === 'string' || typeof value === 'number') {
      cssVars[`--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`] = String(value)
    }
  })
  
  return cssVars
}

// Utility function to apply CSS variables to an element
export function applyCSSVariables(element: HTMLElement, variables: Record<string, string>) {
  Object.entries(variables).forEach(([property, value]) => {
    element.style.setProperty(property, value)
  })
}
