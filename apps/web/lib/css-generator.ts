import { DesignParameters } from '../types/design-parameters'

// Animation speed mappings
const animationSpeedMap = {
  instant: '0ms',
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
  slower: '750ms',
  slowest: '1000ms',
}

// Animation easing mappings
const animationEasingMap = {
  linear: 'linear',
  ease: 'ease',
  'ease-in': 'ease-in',
  'ease-out': 'ease-out',
  'ease-in-out': 'ease-in-out',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  spring: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  'custom-cubic': 'cubic-bezier(0.4, 0, 0.2, 1)',
}

// Card radius mappings
const cardRadiusMap = {
  none: '0px',
  small: '4px',
  medium: '8px',
  large: '16px',
  xl: '24px',
  full: '9999px',
}

// Button size mappings
const buttonSizeMap = {
  xs: { padding: '0.25rem 0.5rem', fontSize: '0.75rem', minHeight: '1.5rem' },
  sm: { padding: '0.375rem 0.75rem', fontSize: '0.875rem', minHeight: '2rem' },
  md: { padding: '0.5rem 1rem', fontSize: '1rem', minHeight: '2.5rem' },
  lg: { padding: '0.75rem 1.5rem', fontSize: '1.125rem', minHeight: '3rem' },
  xl: { padding: '1rem 2rem', fontSize: '1.25rem', minHeight: '3.5rem' },
  '2xl': { padding: '1.25rem 2.5rem', fontSize: '1.5rem', minHeight: '4rem' },
}

// Typography scale mappings
const typographyScaleMap = {
  compact: 1.125,
  standard: 1.25,
  generous: 1.333,
  dramatic: 1.5,
}

// Spacing scale mappings
const spacingScaleMap = {
  tight: 0.5,
  normal: 1,
  loose: 1.5,
  generous: 2,
}

// Container width mappings
const containerWidthMap = {
  narrow: '768px',
  standard: '1024px',
  wide: '1280px',
  full: '100%',
}

// Generate background styles
function generateBackgroundStyles(params: DesignParameters): Record<string, string> {
  const { backgroundType, backgroundIntensity, colors } = params
  
  switch (backgroundType) {
    case 'solid':
      return { background: colors.background }
    
    case 'gradient-linear':
      return {
        background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`
      }
    
    case 'gradient-radial':
      return {
        background: `radial-gradient(circle, ${colors.primary}, ${colors.secondary})`
      }
    
    case 'mesh-gradient':
      return {
        background: `
          radial-gradient(circle at 20% 80%, ${colors.accent}40 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, ${colors.primary}40 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, ${colors.secondary}40 0%, transparent 50%),
          ${colors.background}
        `.replace(/\s+/g, ' ').trim()
      }
    
    case 'glass-morphism':
      return {
        background: `${colors.background}10`,
        backdropFilter: 'blur(10px)',
        border: `1px solid ${colors.border}20`
      }
    
    default:
      return { background: colors.background }
  }
}

// Generate card styles
function generateCardStyles(params: DesignParameters): Record<string, string> {
  const { cardStyle, cardRadius, cardShadow, colors } = params
  const styles: Record<string, string> = {}
  
  // Border radius
  styles.borderRadius = cardRadiusMap[cardRadius]
  
  // Card style specific properties
  switch (cardStyle) {
    case 'flat':
      styles.boxShadow = 'none'
      styles.border = `1px solid ${colors.border}`
      break
    
    case 'elevated':
      styles.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
      break
    
    case 'floating':
      styles.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      break
    
    case 'glass':
      styles.background = `${colors.background}80`
      styles.backdropFilter = 'blur(10px)'
      styles.border = `1px solid ${colors.border}30`
      break
    
    case 'neumorphism':
      styles.background = colors.background
      styles.boxShadow = `8px 8px 16px ${colors.muted}, -8px -8px 16px ${colors.background}`
      break
    
    default:
      styles.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
  }
  
  return styles
}

// Generate button styles
function generateButtonStyles(params: DesignParameters): Record<string, string> {
  const { buttonStyle, buttonSize, buttonHoverEffect, colors } = params
  const sizeConfig = buttonSizeMap[buttonSize]
  const styles: Record<string, string> = {}
  
  // Size properties
  styles.padding = sizeConfig.padding
  styles.fontSize = sizeConfig.fontSize
  styles.minHeight = sizeConfig.minHeight
  
  // Style properties
  switch (buttonStyle) {
    case 'solid':
      styles.background = colors.primary
      styles.color = colors.background
      styles.border = 'none'
      break
    
    case 'outline':
      styles.background = 'transparent'
      styles.color = colors.primary
      styles.border = `2px solid ${colors.primary}`
      break
    
    case 'ghost':
      styles.background = 'transparent'
      styles.color = colors.foreground
      styles.border = 'none'
      break
    
    case 'gradient':
      styles.background = `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`
      styles.color = colors.background
      styles.border = 'none'
      break
    
    default:
      styles.background = colors.primary
      styles.color = colors.background
      styles.border = 'none'
  }
  
  return styles
}

// Main CSS generation function
export function generateCSS(params: DesignParameters): string {
  const backgroundStyles = generateBackgroundStyles(params)
  const cardStyles = generateCardStyles(params)
  const buttonStyles = generateButtonStyles(params)
  
  const cssVariables = {
    // Colors
    '--color-primary': params.colors.primary,
    '--color-secondary': params.colors.secondary,
    '--color-accent': params.colors.accent,
    '--color-background': params.colors.background,
    '--color-foreground': params.colors.foreground,
    '--color-muted': params.colors.muted,
    '--color-border': params.colors.border,
    
    // Animation
    '--animation-speed': animationSpeedMap[params.animationSpeed],
    '--animation-easing': animationEasingMap[params.animationEasing],
    
    // Typography
    '--font-heading': params.fontPairing.heading,
    '--font-body': params.fontPairing.body,
    '--font-accent': params.fontPairing.accent,
    '--typography-scale': typographyScaleMap[params.typographyScale].toString(),
    
    // Spacing
    '--spacing-scale': spacingScaleMap[params.spacingScale].toString(),
    '--container-width': containerWidthMap[params.containerWidth],
    
    // Card
    '--card-radius': cardRadiusMap[params.cardRadius],
    
    // Button
    '--button-padding': buttonSizeMap[params.buttonSize].padding,
    '--button-font-size': buttonSizeMap[params.buttonSize].fontSize,
    '--button-min-height': buttonSizeMap[params.buttonSize].minHeight,
  }
  
  // Generate CSS string
  const cssVarsString = Object.entries(cssVariables)
    .map(([key, value]) => `  ${key}: ${value};`)
    .join('\n')
  
  return `
:root {
${cssVarsString}
}

.dynamic-background {
  ${Object.entries(backgroundStyles).map(([key, value]) => `${key}: ${value};`).join('\n  ')}
}

.dynamic-card {
  ${Object.entries(cardStyles).map(([key, value]) => `${key}: ${value};`).join('\n  ')}
}

.dynamic-button {
  ${Object.entries(buttonStyles).map(([key, value]) => `${key}: ${value};`).join('\n  ')}
  transition: all var(--animation-speed) var(--animation-easing);
}

.dynamic-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
  `.trim()
}

// Generate CSS custom properties object for React styles
export function generateCSSProperties(params: DesignParameters): Record<string, string> {
  return {
    '--color-primary': params.colors.primary,
    '--color-secondary': params.colors.secondary,
    '--color-accent': params.colors.accent,
    '--color-background': params.colors.background,
    '--color-foreground': params.colors.foreground,
    '--color-muted': params.colors.muted,
    '--color-border': params.colors.border,
    '--animation-speed': animationSpeedMap[params.animationSpeed],
    '--animation-easing': animationEasingMap[params.animationEasing],
    '--font-heading': params.fontPairing.heading,
    '--font-body': params.fontPairing.body,
    '--font-accent': params.fontPairing.accent,
    '--typography-scale': typographyScaleMap[params.typographyScale].toString(),
    '--spacing-scale': spacingScaleMap[params.spacingScale].toString(),
    '--container-width': containerWidthMap[params.containerWidth],
    '--card-radius': cardRadiusMap[params.cardRadius],
    '--button-padding': buttonSizeMap[params.buttonSize].padding,
    '--button-font-size': buttonSizeMap[params.buttonSize].fontSize,
    '--button-min-height': buttonSizeMap[params.buttonSize].minHeight,
  }
}
