'use client'

import { useEffect, useRef, useState } from 'react'
import { useDesignStore } from '../store/design-store'
import { cn } from '../lib/utils'

interface AnimatedElementProps {
  children: React.ReactNode
  animation?: 'fade-in' | 'slide-up' | 'slide-down' | 'scale-in' | 'none'
  delay?: number
  className?: string
  triggerOnce?: boolean
}

export function AnimatedElement({ 
  children, 
  animation = 'fade-in', 
  delay = 0, 
  className,
  triggerOnce = true 
}: AnimatedElementProps) {
  const { parameters } = useDesignStore()
  const [isVisible, setIsVisible] = useState(false)
  const [hasTriggered, setHasTriggered] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && (!triggerOnce || !hasTriggered)) {
          setIsVisible(true)
          if (triggerOnce) {
            setHasTriggered(true)
          }
        } else if (!triggerOnce && !entry.isIntersecting) {
          setIsVisible(false)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    )
    
    if (elementRef.current) {
      observer.observe(elementRef.current)
    }
    
    return () => {
      if (elementRef.current) {
        observer.unobserve(elementRef.current)
      }
    }
  }, [triggerOnce, hasTriggered])
  
  const getAnimationClasses = () => {
    if (animation === 'none' || parameters.animationSpeed === 'instant') {
      return ''
    }
    
    const baseClasses = 'transition-all'
    const durationClass = `duration-[var(--animation-speed)]`
    const easingClass = `ease-[var(--animation-easing)]`
    
    if (!isVisible) {
      switch (animation) {
        case 'fade-in':
          return cn(baseClasses, durationClass, easingClass, 'opacity-0')
        case 'slide-up':
          return cn(baseClasses, durationClass, easingClass, 'opacity-0 translate-y-8')
        case 'slide-down':
          return cn(baseClasses, durationClass, easingClass, 'opacity-0 -translate-y-8')
        case 'scale-in':
          return cn(baseClasses, durationClass, easingClass, 'opacity-0 scale-95')
        default:
          return cn(baseClasses, durationClass, easingClass, 'opacity-0')
      }
    }
    
    return cn(baseClasses, durationClass, easingClass, 'opacity-100 translate-y-0 scale-100')
  }
  
  const delayStyle = delay > 0 ? { transitionDelay: `${delay}ms` } : {}
  
  return (
    <div
      ref={elementRef}
      className={cn(getAnimationClasses(), className)}
      style={delayStyle}
    >
      {children}
    </div>
  )
}
