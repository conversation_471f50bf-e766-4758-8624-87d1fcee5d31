'use client'

import { useDesignStore } from '../../store/design-store'
import { cn } from '../../lib/utils'

export function TestimonialsSection() {
  const { parameters } = useDesignStore()
  
  const getCardClasses = () => {
    const baseClasses = 'p-6 transition-all duration-[var(--animation-speed)] ease-[var(--animation-easing)]'
    
    switch (parameters.cardStyle) {
      case 'flat':
        return cn(baseClasses, 'border border-[var(--color-border)] bg-[var(--color-background)]')
      case 'elevated':
        return cn(baseClasses, 'shadow-md bg-[var(--color-background)] hover:shadow-lg')
      case 'floating':
        return cn(baseClasses, 'shadow-xl bg-[var(--color-background)] hover:shadow-2xl hover:-translate-y-1')
      case 'glass':
        return cn(baseClasses, 'backdrop-blur-lg bg-white/10 border border-white/20')
      case 'neumorphism':
        return cn(baseClasses, 'bg-[var(--color-background)] shadow-[8px_8px_16px_var(--color-muted),-8px_-8px_16px_var(--color-background)]')
      case 'outlined':
        return cn(baseClasses, 'border-2 border-[var(--color-primary)] bg-[var(--color-background)]')
      default:
        return cn(baseClasses, 'shadow-md bg-[var(--color-background)]')
    }
  }
  
  const getCardRadius = () => {
    switch (parameters.cardRadius) {
      case 'none': return 'rounded-none'
      case 'small': return 'rounded'
      case 'medium': return 'rounded-lg'
      case 'large': return 'rounded-xl'
      case 'xl': return 'rounded-2xl'
      case 'full': return 'rounded-full'
      default: return 'rounded-lg'
    }
  }
  
  const getContainerClasses = () => {
    const widthClass = parameters.containerWidth === 'narrow' ? 'max-w-4xl' :
                      parameters.containerWidth === 'standard' ? 'max-w-6xl' :
                      parameters.containerWidth === 'wide' ? 'max-w-7xl' : 'max-w-full'
    
    const spacingClass = parameters.spacingScale === 'tight' ? 'py-12' :
                        parameters.spacingScale === 'normal' ? 'py-16' :
                        parameters.spacingScale === 'loose' ? 'py-20' : 'py-24'
    
    return cn('mx-auto px-4 sm:px-6 lg:px-8', widthClass, spacingClass)
  }
  
  const testimonials = [
    {
      quote: "This platform completely transformed how we approach web design. The real-time editing feature is a game-changer!",
      author: "Sarah Johnson",
      role: "Creative Director",
      company: "Design Studio Pro",
      avatar: "👩‍💼"
    },
    {
      quote: "I've never been able to create such professional-looking websites so quickly. The preset templates are incredible.",
      author: "Michael Chen",
      role: "Freelance Designer",
      company: "Independent",
      avatar: "👨‍💻"
    },
    {
      quote: "The parameter system gives us unprecedented control over every aspect of our designs. It's like having a design team in a box.",
      author: "Emily Rodriguez",
      role: "Marketing Manager",
      company: "TechStart Inc.",
      avatar: "👩‍🚀"
    },
    {
      quote: "Our development team loves the clean code export. It integrates seamlessly with our existing workflow.",
      author: "David Kim",
      role: "Lead Developer",
      company: "WebFlow Agency",
      avatar: "👨‍🔧"
    },
    {
      quote: "The responsive design features ensure our websites look perfect on every device. No more manual tweaking!",
      author: "Lisa Thompson",
      role: "UX Designer",
      company: "Mobile First Co.",
      avatar: "👩‍🎨"
    },
    {
      quote: "As a non-technical founder, this platform empowered me to create a professional website for my startup without hiring a designer.",
      author: "Alex Morgan",
      role: "Founder & CEO",
      company: "StartupXYZ",
      avatar: "👨‍💼"
    }
  ]
  
  return (
    <section className="bg-[var(--color-background)] text-[var(--color-foreground)]">
      <div className={getContainerClasses()}>
        <div className="text-center mb-16">
          <h2 
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 text-[var(--color-foreground)]"
            style={{ fontFamily: `var(--font-heading), sans-serif` }}
          >
            What Our Users Say
          </h2>
          <p 
            className="text-xl text-[var(--color-foreground)]/80 max-w-3xl mx-auto leading-relaxed"
            style={{ fontFamily: `var(--font-body), sans-serif` }}
          >
            Don't just take our word for it. Here's what designers, developers, and business owners 
            are saying about our platform.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className={cn(getCardClasses(), getCardRadius())}
            >
              <div className="mb-4">
                <div className="flex text-[var(--color-accent)] mb-2">
                  {[...Array(5)].map((_, i) => (
                    <span key={i} className="text-lg">★</span>
                  ))}
                </div>
                <blockquote 
                  className="text-[var(--color-foreground)]/80 leading-relaxed mb-4"
                  style={{ fontFamily: `var(--font-body), sans-serif` }}
                >
                  "{testimonial.quote}"
                </blockquote>
              </div>
              
              <div className="flex items-center">
                <div className="text-3xl mr-4">{testimonial.avatar}</div>
                <div>
                  <div 
                    className="font-semibold text-[var(--color-foreground)]"
                    style={{ fontFamily: `var(--font-heading), sans-serif` }}
                  >
                    {testimonial.author}
                  </div>
                  <div 
                    className="text-sm text-[var(--color-foreground)]/60"
                    style={{ fontFamily: `var(--font-body), sans-serif` }}
                  >
                    {testimonial.role} at {testimonial.company}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <div className={cn(getCardClasses(), getCardRadius(), 'max-w-2xl mx-auto bg-gradient-to-r from-[var(--color-primary)] to-[var(--color-accent)] text-white')}>
            <h3 
              className="text-2xl font-bold mb-4"
              style={{ fontFamily: `var(--font-heading), sans-serif` }}
            >
              Join Thousands of Happy Users
            </h3>
            <p 
              className="text-lg mb-6 opacity-90"
              style={{ fontFamily: `var(--font-body), sans-serif` }}
            >
              Start creating amazing websites today with our powerful design platform.
            </p>
            <button className="bg-white text-[var(--color-primary)] px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-[var(--animation-speed)]">
              Get Started Free
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
