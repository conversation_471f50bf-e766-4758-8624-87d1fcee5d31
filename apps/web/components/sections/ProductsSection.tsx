'use client'

import { useDesignStore } from '../../store/design-store'
import { cn } from '../../lib/utils'

export function ProductsSection() {
  const { parameters } = useDesignStore()
  
  const getCardClasses = () => {
    const baseClasses = 'p-6 transition-all duration-[var(--animation-speed)] ease-[var(--animation-easing)] group'
    
    switch (parameters.cardStyle) {
      case 'flat':
        return cn(baseClasses, 'border border-[var(--color-border)] bg-[var(--color-background)]')
      case 'elevated':
        return cn(baseClasses, 'shadow-md bg-[var(--color-background)] hover:shadow-lg')
      case 'floating':
        return cn(baseClasses, 'shadow-xl bg-[var(--color-background)] hover:shadow-2xl hover:-translate-y-2')
      case 'glass':
        return cn(baseClasses, 'backdrop-blur-lg bg-white/10 border border-white/20')
      case 'neumorphism':
        return cn(baseClasses, 'bg-[var(--color-background)] shadow-[8px_8px_16px_var(--color-muted),-8px_-8px_16px_var(--color-background)]')
      case 'outlined':
        return cn(baseClasses, 'border-2 border-[var(--color-primary)] bg-[var(--color-background)] hover:border-[var(--color-accent)]')
      case 'gradient-border':
        return cn(baseClasses, 'bg-[var(--color-background)] relative overflow-hidden before:absolute before:inset-0 before:p-[2px] before:bg-gradient-to-r before:from-[var(--color-primary)] before:to-[var(--color-accent)] before:rounded-[inherit] before:-z-10')
      default:
        return cn(baseClasses, 'shadow-md bg-[var(--color-background)]')
    }
  }
  
  const getCardRadius = () => {
    switch (parameters.cardRadius) {
      case 'none': return 'rounded-none'
      case 'small': return 'rounded'
      case 'medium': return 'rounded-lg'
      case 'large': return 'rounded-xl'
      case 'xl': return 'rounded-2xl'
      case 'full': return 'rounded-full'
      default: return 'rounded-lg'
    }
  }
  
  const getButtonClasses = () => {
    const baseClasses = 'w-full inline-flex items-center justify-center rounded-md font-medium transition-all duration-[var(--animation-speed)] ease-[var(--animation-easing)] px-6 py-3'
    
    switch (parameters.buttonStyle) {
      case 'solid':
        return cn(baseClasses, 'bg-[var(--color-primary)] text-white hover:bg-[var(--color-primary)]/90 hover:scale-105')
      case 'outline':
        return cn(baseClasses, 'border-2 border-[var(--color-primary)] text-[var(--color-primary)] hover:bg-[var(--color-primary)] hover:text-white')
      case 'ghost':
        return cn(baseClasses, 'text-[var(--color-primary)] hover:bg-[var(--color-primary)]/10')
      case 'gradient':
        return cn(baseClasses, 'bg-gradient-to-r from-[var(--color-primary)] to-[var(--color-accent)] text-white hover:scale-105')
      default:
        return cn(baseClasses, 'bg-[var(--color-primary)] text-white hover:bg-[var(--color-primary)]/90')
    }
  }
  
  const getContainerClasses = () => {
    const widthClass = parameters.containerWidth === 'narrow' ? 'max-w-4xl' :
                      parameters.containerWidth === 'standard' ? 'max-w-6xl' :
                      parameters.containerWidth === 'wide' ? 'max-w-7xl' : 'max-w-full'
    
    const spacingClass = parameters.spacingScale === 'tight' ? 'py-12' :
                        parameters.spacingScale === 'normal' ? 'py-16' :
                        parameters.spacingScale === 'loose' ? 'py-20' : 'py-24'
    
    return cn('mx-auto px-4 sm:px-6 lg:px-8', widthClass, spacingClass)
  }
  
  const products = [
    {
      name: 'Starter Plan',
      price: '$9',
      period: '/month',
      description: 'Perfect for individuals and small projects',
      features: [
        '5 Website Projects',
        'Basic Templates',
        'Real-time Editing',
        'Mobile Responsive',
        'Email Support'
      ],
      popular: false,
      icon: '🚀'
    },
    {
      name: 'Professional',
      price: '$29',
      period: '/month',
      description: 'Ideal for freelancers and growing businesses',
      features: [
        '25 Website Projects',
        'Premium Templates',
        'Advanced Customization',
        'Priority Support',
        'Code Export',
        'Team Collaboration'
      ],
      popular: true,
      icon: '💼'
    },
    {
      name: 'Enterprise',
      price: '$99',
      period: '/month',
      description: 'For agencies and large organizations',
      features: [
        'Unlimited Projects',
        'Custom Templates',
        'White-label Solution',
        'Dedicated Support',
        'API Access',
        'Advanced Analytics',
        'Custom Integrations'
      ],
      popular: false,
      icon: '🏢'
    }
  ]
  
  return (
    <section className="bg-[var(--color-muted)] text-[var(--color-foreground)]">
      <div className={getContainerClasses()}>
        <div className="text-center mb-16">
          <h2 
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 text-[var(--color-foreground)]"
            style={{ fontFamily: `var(--font-heading), sans-serif` }}
          >
            Choose Your Plan
          </h2>
          <p 
            className="text-xl text-[var(--color-foreground)]/80 max-w-3xl mx-auto leading-relaxed"
            style={{ fontFamily: `var(--font-body), sans-serif` }}
          >
            Select the perfect plan for your needs. All plans include our core features 
            with different levels of customization and support.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {products.map((product, index) => (
            <div 
              key={index}
              className={cn(
                getCardClasses(), 
                getCardRadius(),
                'relative',
                product.popular && 'ring-2 ring-[var(--color-accent)] ring-offset-4 ring-offset-[var(--color-muted)]'
              )}
            >
              {product.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-[var(--color-accent)] text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center mb-6">
                <div className="text-4xl mb-4">{product.icon}</div>
                <h3 
                  className="text-2xl font-bold mb-2 text-[var(--color-foreground)]"
                  style={{ fontFamily: `var(--font-heading), sans-serif` }}
                >
                  {product.name}
                </h3>
                <p 
                  className="text-[var(--color-foreground)]/70 mb-4"
                  style={{ fontFamily: `var(--font-body), sans-serif` }}
                >
                  {product.description}
                </p>
                <div className="flex items-baseline justify-center">
                  <span 
                    className="text-4xl font-bold text-[var(--color-primary)]"
                    style={{ fontFamily: `var(--font-heading), sans-serif` }}
                  >
                    {product.price}
                  </span>
                  <span 
                    className="text-[var(--color-foreground)]/60 ml-1"
                    style={{ fontFamily: `var(--font-body), sans-serif` }}
                  >
                    {product.period}
                  </span>
                </div>
              </div>
              
              <ul className="space-y-3 mb-8">
                {product.features.map((feature, featureIndex) => (
                  <li 
                    key={featureIndex}
                    className="flex items-center text-[var(--color-foreground)]/80"
                    style={{ fontFamily: `var(--font-body), sans-serif` }}
                  >
                    <span className="text-[var(--color-accent)] mr-3">✓</span>
                    {feature}
                  </li>
                ))}
              </ul>
              
              <button className={getButtonClasses()}>
                Get Started
              </button>
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <p 
            className="text-[var(--color-foreground)]/60 mb-4"
            style={{ fontFamily: `var(--font-body), sans-serif` }}
          >
            All plans include a 14-day free trial. No credit card required.
          </p>
          <button className="text-[var(--color-primary)] hover:text-[var(--color-accent)] transition-colors duration-[var(--animation-speed)] font-medium">
            Compare all features →
          </button>
        </div>
      </div>
    </section>
  )
}
