'use client'

import { useDesignStore } from '../../store/design-store'
import { useResponsive } from '../../hooks/use-responsive'
import { AnimatedElement } from '../AnimatedElement'
import { cn } from '../../lib/utils'

export function HeroSection() {
  const { parameters } = useDesignStore()
  const { isMobile, isTablet } = useResponsive()
  
  const getBackgroundClasses = () => {
    switch (parameters.backgroundType) {
      case 'gradient-linear':
        return 'bg-gradient-to-br from-[var(--color-primary)] to-[var(--color-secondary)]'
      case 'gradient-radial':
        return 'bg-gradient-radial from-[var(--color-primary)] to-[var(--color-secondary)]'
      case 'mesh-gradient':
        return 'relative overflow-hidden'
      case 'glass-morphism':
        return 'backdrop-blur-lg bg-white/10 border border-white/20'
      default:
        return 'bg-[var(--color-background)]'
    }
  }
  
  const getTextColorClasses = () => {
    if (parameters.backgroundType === 'gradient-linear' || parameters.backgroundType === 'gradient-radial') {
      return 'text-white'
    }
    return 'text-[var(--color-foreground)]'
  }
  
  const getButtonClasses = () => {
    const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-all duration-[var(--animation-speed)] ease-[var(--animation-easing)]'
    const sizeClasses = 'px-8 py-3 text-lg'
    
    switch (parameters.buttonStyle) {
      case 'solid':
        return cn(baseClasses, sizeClasses, 'bg-[var(--color-accent)] text-white hover:bg-[var(--color-accent)]/90 hover:scale-105 hover:shadow-lg')
      case 'outline':
        return cn(baseClasses, sizeClasses, 'border-2 border-[var(--color-accent)] text-[var(--color-accent)] hover:bg-[var(--color-accent)] hover:text-white')
      case 'ghost':
        return cn(baseClasses, sizeClasses, 'text-[var(--color-accent)] hover:bg-[var(--color-accent)]/10')
      case 'gradient':
        return cn(baseClasses, sizeClasses, 'bg-gradient-to-r from-[var(--color-primary)] to-[var(--color-accent)] text-white hover:scale-105 hover:shadow-lg')
      default:
        return cn(baseClasses, sizeClasses, 'bg-[var(--color-primary)] text-white hover:bg-[var(--color-primary)]/90')
    }
  }
  
  const getContainerClasses = () => {
    const widthClass = parameters.containerWidth === 'narrow' ? 'max-w-4xl' :
                      parameters.containerWidth === 'standard' ? 'max-w-6xl' :
                      parameters.containerWidth === 'wide' ? 'max-w-7xl' : 'max-w-full'
    
    const spacingClass = parameters.spacingScale === 'tight' ? 'py-12' :
                        parameters.spacingScale === 'normal' ? 'py-16' :
                        parameters.spacingScale === 'loose' ? 'py-20' : 'py-24'
    
    return cn('mx-auto px-4 sm:px-6 lg:px-8', widthClass, spacingClass)
  }
  
  return (
    <section className={cn('relative min-h-screen flex items-center', getBackgroundClasses())}>
      {/* Mesh gradient background */}
      {parameters.backgroundType === 'mesh-gradient' && (
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-72 h-72 bg-[var(--color-primary)] rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
          <div className="absolute top-0 right-0 w-72 h-72 bg-[var(--color-accent)] rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-0 left-20 w-72 h-72 bg-[var(--color-secondary)] rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
        </div>
      )}
      
      <div className={getContainerClasses()}>
        <div className="text-center">
          <AnimatedElement animation="fade-in" delay={200}>
            <h1
              className={cn(
                'text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6',
                getTextColorClasses(),
                isMobile && 'text-3xl',
                isTablet && 'text-5xl'
              )}
              style={{ fontFamily: `var(--font-heading), sans-serif` }}
            >
              Build Amazing
              <span className="block text-[var(--color-accent)]">
                Websites
              </span>
            </h1>
          </AnimatedElement>

          <AnimatedElement animation="slide-up" delay={400}>
            <p
              className={cn(
                'text-xl sm:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed',
                getTextColorClasses(),
                'opacity-90',
                isMobile && 'text-lg mb-6',
                isTablet && 'text-xl'
              )}
              style={{ fontFamily: `var(--font-body), sans-serif` }}
            >
              Create stunning, responsive websites with our powerful design system.
              Customize every aspect in real-time and see your vision come to life.
            </p>
          </AnimatedElement>

          <AnimatedElement animation="scale-in" delay={600}>
            <div className={cn(
              'flex gap-4 justify-center items-center',
              isMobile ? 'flex-col' : 'flex-col sm:flex-row'
            )}>
              <button className={getButtonClasses()}>
                Get Started
              </button>
              <button className={cn(
                'inline-flex items-center justify-center rounded-md font-medium transition-all duration-[var(--animation-speed)]',
                'px-8 py-3 text-lg',
                'border-2 border-white/30 text-white hover:bg-white/10',
                isMobile && 'w-full'
              )}>
                Learn More
              </button>
            </div>
          </AnimatedElement>
        </div>
      </div>
      
      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-bounce"></div>
        </div>
      </div>
    </section>
  )
}
