'use client'

import { useDesignStore } from '../../store/design-store'
import { cn } from '../../lib/utils'

export function AboutSection() {
  const { parameters } = useDesignStore()
  
  const getCardClasses = () => {
    const baseClasses = 'p-6 transition-all duration-[var(--animation-speed)] ease-[var(--animation-easing)]'
    
    switch (parameters.cardStyle) {
      case 'flat':
        return cn(baseClasses, 'border border-[var(--color-border)] bg-[var(--color-background)]')
      case 'elevated':
        return cn(baseClasses, 'shadow-md bg-[var(--color-background)] hover:shadow-lg')
      case 'floating':
        return cn(baseClasses, 'shadow-xl bg-[var(--color-background)] hover:shadow-2xl hover:-translate-y-1')
      case 'glass':
        return cn(baseClasses, 'backdrop-blur-lg bg-white/10 border border-white/20')
      case 'neumorphism':
        return cn(baseClasses, 'bg-[var(--color-background)] shadow-[8px_8px_16px_var(--color-muted),-8px_-8px_16px_var(--color-background)]')
      case 'outlined':
        return cn(baseClasses, 'border-2 border-[var(--color-primary)] bg-[var(--color-background)]')
      case 'organic':
        return cn(baseClasses, 'bg-[var(--color-background)] shadow-lg')
      default:
        return cn(baseClasses, 'shadow-md bg-[var(--color-background)]')
    }
  }
  
  const getCardRadius = () => {
    switch (parameters.cardRadius) {
      case 'none': return 'rounded-none'
      case 'small': return 'rounded'
      case 'medium': return 'rounded-lg'
      case 'large': return 'rounded-xl'
      case 'xl': return 'rounded-2xl'
      case 'full': return 'rounded-full'
      default: return 'rounded-lg'
    }
  }
  
  const getContainerClasses = () => {
    const widthClass = parameters.containerWidth === 'narrow' ? 'max-w-4xl' :
                      parameters.containerWidth === 'standard' ? 'max-w-6xl' :
                      parameters.containerWidth === 'wide' ? 'max-w-7xl' : 'max-w-full'
    
    const spacingClass = parameters.spacingScale === 'tight' ? 'py-12' :
                        parameters.spacingScale === 'normal' ? 'py-16' :
                        parameters.spacingScale === 'loose' ? 'py-20' : 'py-24'
    
    return cn('mx-auto px-4 sm:px-6 lg:px-8', widthClass, spacingClass)
  }
  
  const features = [
    {
      title: 'Real-time Editing',
      description: 'See your changes instantly as you adjust design parameters. No need to refresh or wait for builds.',
      icon: '⚡'
    },
    {
      title: 'Professional Templates',
      description: 'Start with industry-specific presets designed by professionals for maximum impact.',
      icon: '🎨'
    },
    {
      title: 'Responsive Design',
      description: 'Every design automatically adapts to all screen sizes and devices seamlessly.',
      icon: '📱'
    },
    {
      title: 'Export Ready',
      description: 'Export your designs as clean, production-ready code that developers will love.',
      icon: '💻'
    }
  ]
  
  return (
    <section className="bg-[var(--color-muted)] text-[var(--color-foreground)]">
      <div className={getContainerClasses()}>
        <div className="text-center mb-16">
          <h2 
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 text-[var(--color-foreground)]"
            style={{ fontFamily: `var(--font-heading), sans-serif` }}
          >
            About Our Platform
          </h2>
          <p 
            className="text-xl text-[var(--color-foreground)]/80 max-w-3xl mx-auto leading-relaxed"
            style={{ fontFamily: `var(--font-body), sans-serif` }}
          >
            We believe that great design should be accessible to everyone. Our platform combines 
            powerful design tools with intuitive controls to help you create stunning websites 
            without the complexity.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className={cn(getCardClasses(), getCardRadius())}
            >
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 
                className="text-xl font-semibold mb-3 text-[var(--color-foreground)]"
                style={{ fontFamily: `var(--font-heading), sans-serif` }}
              >
                {feature.title}
              </h3>
              <p 
                className="text-[var(--color-foreground)]/70 leading-relaxed"
                style={{ fontFamily: `var(--font-body), sans-serif` }}
              >
                {feature.description}
              </p>
            </div>
          ))}
        </div>
        
        <div className="mt-16 text-center">
          <div className={cn(getCardClasses(), getCardRadius(), 'max-w-4xl mx-auto')}>
            <h3 
              className="text-2xl font-bold mb-4 text-[var(--color-foreground)]"
              style={{ fontFamily: `var(--font-heading), sans-serif` }}
            >
              Our Mission
            </h3>
            <p 
              className="text-lg text-[var(--color-foreground)]/80 leading-relaxed"
              style={{ fontFamily: `var(--font-body), sans-serif` }}
            >
              To democratize web design by providing powerful, intuitive tools that enable anyone 
              to create professional-quality websites. We're building the future of web design, 
              one parameter at a time.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
