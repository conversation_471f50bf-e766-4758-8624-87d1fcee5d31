'use client'

import { useState } from 'react'
import { useDesignStore } from '../store/design-store'
import { designPresets, presetDescriptions } from '../presets/design-presets'
import { SelectControl } from './controls/SelectControl'
import { ColorControl } from './controls/ColorControl'
import { TextControl } from './controls/TextControl'
import { cn } from '../lib/utils'
import { ChevronRight, ChevronDown, Settings, Palette, Type, Layout, Sparkles } from 'lucide-react'

export function ControlPanel() {
  const { parameters, updateParameter, updateNestedParameter, loadPreset, resetParameters, exportParameters, importParameters } = useDesignStore()
  const [isOpen, setIsOpen] = useState(false)
  const [activeSection, setActiveSection] = useState<string>('background')
  const [showImportExport, setShowImportExport] = useState(false)
  
  const sections = [
    { id: 'background', label: 'Background', icon: Palette },
    { id: 'cards', label: 'Cards & Components', icon: Layout },
    { id: 'buttons', label: 'Buttons', icon: Settings },
    { id: 'typography', label: 'Typography', icon: Type },
    { id: 'colors', label: 'Colors', icon: Palette },
    { id: 'layout', label: 'Layout & Spacing', icon: Layout },
    { id: 'effects', label: 'Visual Effects', icon: Sparkles },
  ]
  
  const backgroundTypeOptions = [
    { value: 'solid', label: 'Solid Color' },
    { value: 'gradient-linear', label: 'Linear Gradient' },
    { value: 'gradient-radial', label: 'Radial Gradient' },
    { value: 'gradient-conic', label: 'Conic Gradient' },
    { value: 'mesh-gradient', label: 'Mesh Gradient' },
    { value: 'glass-morphism', label: 'Glass Morphism' },
    { value: 'texture-subtle', label: 'Subtle Texture' },
    { value: 'texture-paper', label: 'Paper Texture' },
    { value: 'pattern-geometric', label: 'Geometric Pattern' },
    { value: 'pattern-organic', label: 'Organic Pattern' },
  ]
  
  const cardStyleOptions = [
    { value: 'flat', label: 'Flat' },
    { value: 'elevated', label: 'Elevated' },
    { value: 'floating', label: 'Floating' },
    { value: 'outlined', label: 'Outlined' },
    { value: 'glass', label: 'Glass' },
    { value: 'neumorphism', label: 'Neumorphism' },
    { value: 'organic', label: 'Organic' },
    { value: 'gradient-border', label: 'Gradient Border' },
  ]
  
  const buttonStyleOptions = [
    { value: 'solid', label: 'Solid' },
    { value: 'outline', label: 'Outline' },
    { value: 'ghost', label: 'Ghost' },
    { value: 'gradient', label: 'Gradient' },
    { value: 'glass', label: 'Glass' },
    { value: 'pill', label: 'Pill' },
  ]
  
  const renderSection = () => {
    switch (activeSection) {
      case 'background':
        return (
          <div className="space-y-4">
            <SelectControl
              label="Background Type"
              value={parameters.backgroundType}
              options={backgroundTypeOptions}
              onChange={(value) => updateParameter('backgroundType', value as any)}
            />
            <SelectControl
              label="Background Intensity"
              value={parameters.backgroundIntensity}
              options={[
                { value: 'subtle', label: 'Subtle' },
                { value: 'moderate', label: 'Moderate' },
                { value: 'bold', label: 'Bold' },
                { value: 'dramatic', label: 'Dramatic' },
              ]}
              onChange={(value) => updateParameter('backgroundIntensity', value as any)}
            />
            <SelectControl
              label="Background Movement"
              value={parameters.backgroundMovement}
              options={[
                { value: 'static', label: 'Static' },
                { value: 'parallax', label: 'Parallax' },
                { value: 'floating', label: 'Floating' },
                { value: 'animated-gradient', label: 'Animated Gradient' },
              ]}
              onChange={(value) => updateParameter('backgroundMovement', value as any)}
            />
          </div>
        )
      
      case 'cards':
        return (
          <div className="space-y-4">
            <SelectControl
              label="Card Style"
              value={parameters.cardStyle}
              options={cardStyleOptions}
              onChange={(value) => updateParameter('cardStyle', value as any)}
            />
            <SelectControl
              label="Card Radius"
              value={parameters.cardRadius}
              options={[
                { value: 'none', label: 'None' },
                { value: 'small', label: 'Small' },
                { value: 'medium', label: 'Medium' },
                { value: 'large', label: 'Large' },
                { value: 'xl', label: 'Extra Large' },
                { value: 'full', label: 'Full' },
              ]}
              onChange={(value) => updateParameter('cardRadius', value as any)}
            />
            <SelectControl
              label="Card Shadow"
              value={parameters.cardShadow}
              options={[
                { value: 'none', label: 'None' },
                { value: 'subtle', label: 'Subtle' },
                { value: 'medium', label: 'Medium' },
                { value: 'large', label: 'Large' },
                { value: 'colored', label: 'Colored' },
                { value: 'glow', label: 'Glow' },
              ]}
              onChange={(value) => updateParameter('cardShadow', value as any)}
            />
          </div>
        )
      
      case 'buttons':
        return (
          <div className="space-y-4">
            <SelectControl
              label="Button Style"
              value={parameters.buttonStyle}
              options={buttonStyleOptions}
              onChange={(value) => updateParameter('buttonStyle', value as any)}
            />
            <SelectControl
              label="Button Size"
              value={parameters.buttonSize}
              options={[
                { value: 'xs', label: 'Extra Small' },
                { value: 'sm', label: 'Small' },
                { value: 'md', label: 'Medium' },
                { value: 'lg', label: 'Large' },
                { value: 'xl', label: 'Extra Large' },
              ]}
              onChange={(value) => updateParameter('buttonSize', value as any)}
            />
            <SelectControl
              label="Hover Effect"
              value={parameters.buttonHoverEffect}
              options={[
                { value: 'none', label: 'None' },
                { value: 'scale', label: 'Scale' },
                { value: 'lift', label: 'Lift' },
                { value: 'glow', label: 'Glow' },
                { value: 'slide', label: 'Slide' },
                { value: 'pulse', label: 'Pulse' },
              ]}
              onChange={(value) => updateParameter('buttonHoverEffect', value as any)}
            />
          </div>
        )
      
      case 'typography':
        return (
          <div className="space-y-4">
            <TextControl
              label="Heading Font"
              value={parameters.fontPairing.heading}
              onChange={(value) => updateNestedParameter('fontPairing', 'heading', value)}
              placeholder="Inter, Arial, sans-serif"
            />
            <TextControl
              label="Body Font"
              value={parameters.fontPairing.body}
              onChange={(value) => updateNestedParameter('fontPairing', 'body', value)}
              placeholder="Inter, Arial, sans-serif"
            />
            <SelectControl
              label="Typography Scale"
              value={parameters.typographyScale}
              options={[
                { value: 'compact', label: 'Compact' },
                { value: 'standard', label: 'Standard' },
                { value: 'generous', label: 'Generous' },
                { value: 'dramatic', label: 'Dramatic' },
              ]}
              onChange={(value) => updateParameter('typographyScale', value as any)}
            />
            <SelectControl
              label="Typography Weight"
              value={parameters.typographyWeight}
              options={[
                { value: 'light', label: 'Light' },
                { value: 'normal', label: 'Normal' },
                { value: 'medium', label: 'Medium' },
                { value: 'semibold', label: 'Semi Bold' },
                { value: 'bold', label: 'Bold' },
              ]}
              onChange={(value) => updateParameter('typographyWeight', value as any)}
            />
          </div>
        )
      
      case 'colors':
        return (
          <div className="space-y-4">
            <ColorControl
              label="Primary Color"
              value={parameters.colors.primary}
              onChange={(value) => updateNestedParameter('colors', 'primary', value)}
            />
            <ColorControl
              label="Secondary Color"
              value={parameters.colors.secondary}
              onChange={(value) => updateNestedParameter('colors', 'secondary', value)}
            />
            <ColorControl
              label="Accent Color"
              value={parameters.colors.accent}
              onChange={(value) => updateNestedParameter('colors', 'accent', value)}
            />
            <ColorControl
              label="Background Color"
              value={parameters.colors.background}
              onChange={(value) => updateNestedParameter('colors', 'background', value)}
            />
            <ColorControl
              label="Text Color"
              value={parameters.colors.foreground}
              onChange={(value) => updateNestedParameter('colors', 'foreground', value)}
            />
          </div>
        )
      
      case 'layout':
        return (
          <div className="space-y-4">
            <SelectControl
              label="Container Width"
              value={parameters.containerWidth}
              options={[
                { value: 'narrow', label: 'Narrow (768px)' },
                { value: 'standard', label: 'Standard (1024px)' },
                { value: 'wide', label: 'Wide (1280px)' },
                { value: 'full', label: 'Full Width' },
              ]}
              onChange={(value) => updateParameter('containerWidth', value as any)}
            />
            <SelectControl
              label="Spacing Scale"
              value={parameters.spacingScale}
              options={[
                { value: 'tight', label: 'Tight' },
                { value: 'normal', label: 'Normal' },
                { value: 'loose', label: 'Loose' },
                { value: 'generous', label: 'Generous' },
              ]}
              onChange={(value) => updateParameter('spacingScale', value as any)}
            />
            <SelectControl
              label="Layout Density"
              value={parameters.layoutDensity}
              options={[
                { value: 'compact', label: 'Compact' },
                { value: 'balanced', label: 'Balanced' },
                { value: 'spacious', label: 'Spacious' },
                { value: 'minimal', label: 'Minimal' },
              ]}
              onChange={(value) => updateParameter('layoutDensity', value as any)}
            />
          </div>
        )
      
      default:
        return <div>Select a section to edit parameters</div>
    }
  }
  
  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed top-4 right-4 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
      >
        <Settings className="w-6 h-6" />
      </button>
      
      {/* Control Panel */}
      <div className={cn(
        'fixed top-0 right-0 h-full w-80 bg-white shadow-2xl transform transition-transform duration-300 z-40 overflow-hidden',
        isOpen ? 'translate-x-0' : 'translate-x-full'
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Design Controls</h2>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ×
              </button>
            </div>
            
            {/* Presets */}
            <div className="mt-4">
              <label className="text-sm font-medium text-gray-700 mb-3 block">Design Presets</label>
              <div className="space-y-2">
                {Object.entries(designPresets).map(([key, preset]) => (
                  <div key={key} className="group">
                    <button
                      onClick={() => loadPreset(preset)}
                      className="w-full px-3 py-3 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200 hover:border-blue-300"
                    >
                      <div className="font-medium text-blue-900 capitalize text-sm">
                        {key}
                      </div>
                      <div className="text-xs text-blue-600 mt-1 leading-tight">
                        {presetDescriptions[key as keyof typeof presetDescriptions]}
                      </div>
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2 mt-3">
                <button
                  onClick={resetParameters}
                  className="flex-1 px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors border border-gray-200"
                >
                  Reset
                </button>
                <button
                  onClick={() => setShowImportExport(!showImportExport)}
                  className="flex-1 px-3 py-2 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors border border-green-200"
                >
                  Import/Export
                </button>
              </div>

              {/* Import/Export Section */}
              {showImportExport && (
                <div className="mt-3 p-3 bg-gray-50 rounded-lg border">
                  <div className="space-y-2">
                    <button
                      onClick={() => {
                        const config = exportParameters()
                        navigator.clipboard.writeText(config)
                        alert('Configuration copied to clipboard!')
                      }}
                      className="w-full px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                    >
                      Copy Configuration
                    </button>
                    <button
                      onClick={() => {
                        const config = prompt('Paste your configuration JSON:')
                        if (config) {
                          try {
                            importParameters(config)
                            alert('Configuration imported successfully!')
                          } catch (error) {
                            alert('Invalid configuration format')
                          }
                        }
                      }}
                      className="w-full px-3 py-2 text-sm bg-purple-100 text-purple-700 rounded hover:bg-purple-200 transition-colors"
                    >
                      Import Configuration
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Navigation */}
          <div className="border-b border-gray-200">
            <nav className="flex flex-col">
              {sections.map((section) => {
                const Icon = section.icon
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={cn(
                      'flex items-center px-4 py-3 text-sm font-medium transition-colors',
                      activeSection === section.id
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                        : 'text-gray-700 hover:bg-gray-50'
                    )}
                  >
                    <Icon className="w-4 h-4 mr-3" />
                    {section.label}
                    {activeSection === section.id ? (
                      <ChevronDown className="w-4 h-4 ml-auto" />
                    ) : (
                      <ChevronRight className="w-4 h-4 ml-auto" />
                    )}
                  </button>
                )
              })}
            </nav>
          </div>
          
          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {renderSection()}
          </div>
        </div>
      </div>
      
      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-30"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  )
}
