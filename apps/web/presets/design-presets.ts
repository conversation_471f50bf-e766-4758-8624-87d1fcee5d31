import { DesignParameters } from '../types/design-parameters'

// Technology/SaaS Preset
export const techPreset: DesignParameters = {
  backgroundType: 'mesh-gradient',
  backgroundIntensity: 'moderate',
  backgroundMovement: 'static',
  
  cardStyle: 'glass',
  cardRadius: 'large',
  cardShadow: 'medium',
  
  buttonStyle: 'gradient',
  buttonSize: 'md',
  buttonHoverEffect: 'lift',
  
  animationSpeed: 'fast',
  animationEasing: 'ease-out',
  interactionType: 'sophisticated',
  
  fontPairing: {
    heading: 'Inter',
    body: 'Inter',
    accent: 'JetBrains Mono'
  },
  typographyScale: 'standard',
  typographySpacing: 'normal',
  typographyWeight: 'medium',
  
  colorStrategy: 'complementary',
  colorIntensity: 'vibrant',
  colorDistribution: 'neutral-base',
  
  spacingScale: 'normal',
  layoutDensity: 'balanced',
  gridSystem: 'flexible',
  containerWidth: 'wide',
  
  glassEffect: {
    blur: 'medium',
    opacity: '20%',
    border: 'subtle',
    backdrop: 'blur'
  },
  glowEffect: {
    intensity: 'medium',
    color: 'brand',
    spread: 'medium',
    animation: 'pulse'
  },
  shadowComplexity: 'layered',
  
  colors: {
    primary: '#3B82F6',
    secondary: '#1E40AF',
    accent: '#06B6D4',
    background: '#FFFFFF',
    foreground: '#0F172A',
    muted: '#F1F5F9',
    border: '#E2E8F0'
  }
}

// Luxury/Premium Preset
export const luxuryPreset: DesignParameters = {
  backgroundType: 'gradient-linear',
  backgroundIntensity: 'subtle',
  backgroundMovement: 'static',
  
  cardStyle: 'elevated',
  cardRadius: 'small',
  cardShadow: 'large',
  
  buttonStyle: 'outline',
  buttonSize: 'lg',
  buttonHoverEffect: 'glow',
  
  animationSpeed: 'slow',
  animationEasing: 'ease-in-out',
  interactionType: 'sophisticated',
  
  fontPairing: {
    heading: 'Playfair Display',
    body: 'Inter',
    accent: 'Playfair Display'
  },
  typographyScale: 'generous',
  typographySpacing: 'loose',
  typographyWeight: 'light',
  
  colorStrategy: 'monochromatic',
  colorIntensity: 'muted',
  colorDistribution: 'primary-dominant',
  
  spacingScale: 'generous',
  layoutDensity: 'spacious',
  gridSystem: 'standard-12',
  containerWidth: 'standard',
  
  glassEffect: {
    blur: 'subtle',
    opacity: '10%',
    border: 'visible',
    backdrop: 'blur'
  },
  glowEffect: {
    intensity: 'subtle',
    color: 'accent',
    spread: 'wide',
    animation: 'static'
  },
  shadowComplexity: 'realistic',
  
  colors: {
    primary: '#1F2937',
    secondary: '#374151',
    accent: '#D97706',
    background: '#FEFEFE',
    foreground: '#111827',
    muted: '#F9FAFB',
    border: '#E5E7EB'
  }
}

// Sports/Fitness Preset
export const sportsPreset: DesignParameters = {
  backgroundType: 'gradient-linear',
  backgroundIntensity: 'bold',
  backgroundMovement: 'static',
  
  cardStyle: 'floating',
  cardRadius: 'medium',
  cardShadow: 'colored',
  
  buttonStyle: 'solid',
  buttonSize: 'lg',
  buttonHoverEffect: 'scale',
  
  animationSpeed: 'fast',
  animationEasing: 'bounce',
  interactionType: 'playful',
  
  fontPairing: {
    heading: 'Oswald',
    body: 'Open Sans',
    accent: 'Oswald'
  },
  typographyScale: 'standard',
  typographySpacing: 'tight',
  typographyWeight: 'bold',
  
  colorStrategy: 'high-contrast',
  colorIntensity: 'electric',
  colorDistribution: 'accent-heavy',
  
  spacingScale: 'normal',
  layoutDensity: 'compact',
  gridSystem: 'standard-12',
  containerWidth: 'wide',
  
  glassEffect: {
    blur: 'none',
    opacity: '10%',
    border: 'none',
    backdrop: 'blur'
  },
  glowEffect: {
    intensity: 'strong',
    color: 'accent',
    spread: 'tight',
    animation: 'pulse'
  },
  shadowComplexity: 'dynamic',
  
  colors: {
    primary: '#DC2626',
    secondary: '#B91C1C',
    accent: '#F59E0B',
    background: '#FFFFFF',
    foreground: '#1F2937',
    muted: '#F3F4F6',
    border: '#E5E7EB'
  }
}

// Organic/Natural Preset
export const organicPreset: DesignParameters = {
  backgroundType: 'texture-paper',
  backgroundIntensity: 'subtle',
  backgroundMovement: 'static',
  
  cardStyle: 'organic',
  cardRadius: 'xl',
  cardShadow: 'subtle',
  
  buttonStyle: 'pill',
  buttonSize: 'md',
  buttonHoverEffect: 'lift',
  
  animationSpeed: 'slow',
  animationEasing: 'ease-in-out',
  interactionType: 'minimal',
  
  fontPairing: {
    heading: 'Merriweather',
    body: 'Source Sans Pro',
    accent: 'Dancing Script'
  },
  typographyScale: 'generous',
  typographySpacing: 'loose',
  typographyWeight: 'normal',
  
  colorStrategy: 'analogous',
  colorIntensity: 'muted',
  colorDistribution: 'neutral-base',
  
  spacingScale: 'generous',
  layoutDensity: 'spacious',
  gridSystem: 'flexible',
  containerWidth: 'narrow',
  
  glassEffect: {
    blur: 'subtle',
    opacity: '20%',
    border: 'subtle',
    backdrop: 'saturate'
  },
  glowEffect: {
    intensity: 'subtle',
    color: 'accent',
    spread: 'wide',
    animation: 'breathing'
  },
  shadowComplexity: 'simple',
  
  colors: {
    primary: '#059669',
    secondary: '#047857',
    accent: '#D97706',
    background: '#FFFEF7',
    foreground: '#1F2937',
    muted: '#F7F7F0',
    border: '#D6D3D1'
  }
}

// Minimal/Clean Preset
export const minimalPreset: DesignParameters = {
  backgroundType: 'solid',
  backgroundIntensity: 'subtle',
  backgroundMovement: 'static',

  cardStyle: 'flat',
  cardRadius: 'none',
  cardShadow: 'none',

  buttonStyle: 'outline',
  buttonSize: 'md',
  buttonHoverEffect: 'none',

  animationSpeed: 'instant',
  animationEasing: 'linear',
  interactionType: 'minimal',

  fontPairing: {
    heading: 'Helvetica',
    body: 'Helvetica',
    accent: 'Helvetica'
  },
  typographyScale: 'compact',
  typographySpacing: 'tight',
  typographyWeight: 'light',

  colorStrategy: 'monochromatic',
  colorIntensity: 'muted',
  colorDistribution: 'neutral-base',

  spacingScale: 'tight',
  layoutDensity: 'minimal',
  gridSystem: 'standard-12',
  containerWidth: 'narrow',

  glassEffect: {
    blur: 'none',
    opacity: '10%',
    border: 'none',
    backdrop: 'blur'
  },
  glowEffect: {
    intensity: 'subtle',
    color: 'white',
    spread: 'tight',
    animation: 'static'
  },
  shadowComplexity: 'simple',

  colors: {
    primary: '#000000',
    secondary: '#333333',
    accent: '#666666',
    background: '#FFFFFF',
    foreground: '#000000',
    muted: '#F8F8F8',
    border: '#E0E0E0'
  }
}

// Creative/Artistic Preset
export const creativePreset: DesignParameters = {
  backgroundType: 'pattern-organic',
  backgroundIntensity: 'bold',
  backgroundMovement: 'floating',

  cardStyle: 'organic',
  cardRadius: 'xl',
  cardShadow: 'artistic',

  buttonStyle: 'gradient',
  buttonSize: 'lg',
  buttonHoverEffect: 'morph',

  animationSpeed: 'slow',
  animationEasing: 'elastic',
  interactionType: 'playful',

  fontPairing: {
    heading: 'Comfortaa',
    body: 'Open Sans',
    accent: 'Dancing Script'
  },
  typographyScale: 'dramatic',
  typographySpacing: 'loose',
  typographyWeight: 'bold',

  colorStrategy: 'triadic',
  colorIntensity: 'electric',
  colorDistribution: 'gradient-flow',

  spacingScale: 'generous',
  layoutDensity: 'spacious',
  gridSystem: 'asymmetric',
  containerWidth: 'wide',

  glassEffect: {
    blur: 'strong',
    opacity: '30%',
    border: 'visible',
    backdrop: 'saturate'
  },
  glowEffect: {
    intensity: 'dramatic',
    color: 'custom',
    spread: 'wide',
    animation: 'breathing'
  },
  shadowComplexity: 'artistic',

  colors: {
    primary: '#FF6B6B',
    secondary: '#4ECDC4',
    accent: '#FFE66D',
    background: '#F7F1E3',
    foreground: '#2C3E50',
    muted: '#ECF0F1',
    border: '#BDC3C7'
  }
}

export const designPresets = {
  tech: techPreset,
  luxury: luxuryPreset,
  sports: sportsPreset,
  organic: organicPreset,
  minimal: minimalPreset,
  creative: creativePreset,
}

export const presetDescriptions = {
  tech: 'Modern, clean design perfect for technology companies and SaaS products',
  luxury: 'Elegant, sophisticated styling for premium brands and luxury services',
  sports: 'Bold, energetic design ideal for fitness and sports-related businesses',
  organic: 'Natural, earthy aesthetic perfect for eco-friendly and wellness brands',
  minimal: 'Clean, simple design focusing on content and functionality',
  creative: 'Artistic, expressive styling for creative agencies and portfolios',
}
