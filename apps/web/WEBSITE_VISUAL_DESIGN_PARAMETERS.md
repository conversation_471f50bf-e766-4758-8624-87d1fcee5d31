# Website Visual Design Parameters

## Overview

This document defines comprehensive visual design parameters for AI-powered website generation based on branding information. These parameters enable the Website AI Agent to make intelligent UI/UX decisions that align with brand personality, industry context, and target audience preferences.

## 1. Parameter Categories

### 1.1 Background Styling

#### `backgroundType`
- **Type**: `enum`
- **Values**: 
  - `solid` - Single color backgrounds
  - `gradient-linear` - Linear gradients (2-4 colors)
  - `gradient-radial` - Radial gradients (center-out)
  - `gradient-conic` - Conic/angular gradients
  - `texture-subtle` - Subtle noise/grain textures
  - `texture-paper` - Paper-like textures
  - `texture-fabric` - Fabric/canvas textures
  - `pattern-geometric` - Geometric patterns (triangles, hexagons)
  - `pattern-organic` - Organic patterns (waves, curves)
  - `pattern-dots` - Dot patterns (polka dots, scattered)
  - `pattern-lines` - Line patterns (stripes, grids)
  - `image-hero` - Large hero images
  - `image-subtle` - Subtle background images
  - `mesh-gradient` - Modern mesh gradients
  - `glass-morphism` - Frosted glass effect

#### `backgroundIntensity`
- **Type**: `enum`
- **Values**: `subtle`, `moderate`, `bold`, `dramatic`
- **CSS Mapping**: Opacity, contrast, saturation levels

#### `backgroundMovement`
- **Type**: `enum`
- **Values**: `static`, `parallax`, `floating`, `animated-gradient`, `particle-system`

### 1.2 Card and Component Styling

#### `cardStyle`
- **Type**: `enum`
- **Values**:
  - `flat` - No shadows, minimal borders
  - `elevated` - Subtle drop shadows
  - `floating` - Prominent shadows with blur
  - `outlined` - Border-focused design
  - `glass` - Glass morphism effect
  - `neumorphism` - Soft, extruded appearance
  - `brutalist` - Bold, stark contrasts
  - `organic` - Rounded, natural shapes
  - `geometric` - Sharp, angular shapes
  - `gradient-border` - Gradient borders
  - `neon-glow` - Glowing effects
  - `paper` - Paper-like texture and shadow

#### `cardRadius`
- **Type**: `enum`
- **Values**: `none` (0px), `small` (4px), `medium` (8px), `large` (16px), `xl` (24px), `full` (9999px)
- **Note**: By default, cardRadius applies uniformly to all corners. It is also possible to apply radius selectively to individual corners if needed.
- **Example**: 
```
.card {
  border-top-left-radius: 16px;   /* rounded only on top-left */
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
```

#### `cardShadow`
- **Type**: `enum`
- **Values**:
  - `none` - No shadow
  - `subtle` - Light, close shadow
  - `medium` - Standard drop shadow
  - `large` - Prominent shadow
  - `colored` - Shadow with brand color tint
  - `multi-layer` - Multiple shadow layers
  - `inner` - Inner/inset shadows
  - `glow` - Soft glow effect

### 1.3 Button Design

#### `buttonStyle`
- **Type**: `enum`
- **Values**:
  - `solid` - Filled background
  - `outline` - Border only
  - `ghost` - Transparent with hover
  - `gradient` - Gradient background
  - `glass` - Glass morphism
  - `neumorphic` - Soft 3D effect
  - `floating` - Elevated appearance
  - `pill` - Fully rounded
  - `sharp` - Angular corners
  - `split` - Two-tone design
  - `animated` - Built-in animations
  - `neon` - Glowing neon effect

#### `buttonSize`
- **Type**: `enum`
- **Values**: `xs`, `sm`, `md`, `lg`, `xl`, `2xl`
- **CSS Mapping**: Padding, font-size, min-height

#### `buttonHoverEffect`
- **Type**: `enum`
- **Values**:
  - `scale` - Slight size increase
  - `lift` - Shadow elevation
  - `glow` - Glow effect
  - `slide` - Background slide
  - `rotate` - Subtle rotation
  - `pulse` - Pulsing animation
  - `ripple` - Material ripple
  - `morph` - Shape transformation
  - `color-shift` - Color transition
  - `none` - No hover effect

### 1.4 Animation Behavior

#### `animationSpeed`
- **Type**: `enum`
- **Values**: 
  - `instant` (0ms)
  - `fast` (150ms)
  - `normal` (300ms)
  - `slow` (500ms)
  - `slower` (750ms)
  - `slowest` (1000ms)

#### `animationEasing`
- **Type**: `enum`
- **Values**:
  - `linear` - Constant speed
  - `ease` - Standard ease
  - `ease-in` - Slow start
  - `ease-out` - Slow end
  - `ease-in-out` - Slow start and end
  - `bounce` - Bouncy effect
  - `elastic` - Elastic effect
  - `spring` - Spring physics
  - `custom-cubic` - Custom cubic-bezier

#### `interactionType`
- **Type**: `enum`
- **Values**:
  - `minimal` - Subtle feedback only
  - `standard` - Standard hover/focus states
  - `playful` - Fun, bouncy interactions
  - `sophisticated` - Smooth, elegant transitions
  - `dramatic` - Bold, attention-grabbing effects
  - `micro` - Micro-interactions and details
  - `immersive` - 3D and parallax effects

### 1.5 Typography Styling

#### `fontPairing`
- **Type**: `object`
- **Structure**:
  ```json
  {
    "heading": "font-family-name",
    "body": "font-family-name",
    "accent": "font-family-name"
  }
  ```
- **Categories**:
  - `classic` - Traditional serif + sans-serif
  - `modern` - Contemporary sans-serif pairs
  - `editorial` - Magazine-style combinations
  - `tech` - Monospace + geometric sans
  - `luxury` - Elegant serif + refined sans
  - `playful` - Rounded + script combinations
  - `minimal` - Single font family variations
  - `contrast` - High contrast pairings

#### `typographyScale`
- **Type**: `enum`
- **Values**: `compact`, `standard`, `generous`, `dramatic`
- **CSS Mapping**: Font-size ratios (1.125, 1.25, 1.333, 1.5)

#### `typographySpacing`
- **Type**: `enum`
- **Values**:
  - `tight` - Reduced line-height and letter-spacing
  - `normal` - Standard spacing
  - `loose` - Increased spacing for readability
  - `dramatic` - Very loose for impact

#### `typographyWeight`
- **Type**: `enum`
- **Values**: `light`, `normal`, `medium`, `semibold`, `bold`, `extrabold`

### 1.6 Color Application Strategies

#### `colorStrategy`
- **Type**: `enum`
- **Values**:
  - `monochromatic` - Single hue variations
  - `analogous` - Adjacent colors on color wheel
  - `complementary` - Opposite colors
  - `triadic` - Three evenly spaced colors
  - `split-complementary` - Base + two adjacent to complement
  - `tetradic` - Four colors forming rectangle
  - `neutral-accent` - Neutral base + single accent
  - `gradient-heavy` - Gradient-focused design
  - `high-contrast` - Bold contrast ratios
  - `low-contrast` - Subtle, muted contrasts

#### `colorIntensity`
- **Type**: `enum`
- **Values**: `muted`, `balanced`, `vibrant`, `electric`
- **CSS Mapping**: Saturation and brightness levels

#### `colorDistribution`
- **Type**: `enum`
- **Values**:
  - `primary-dominant` - 60% primary, 30% secondary, 10% accent
  - `balanced` - Even distribution
  - `accent-heavy` - Bold accent usage
  - `neutral-base` - Neutral foundation with color highlights
  - `gradient-flow` - Flowing color transitions

### 1.7 Layout and Spacing Parameters

#### `spacingScale`
- **Type**: `enum`
- **Values**: `tight`, `normal`, `loose`, `generous`
- **CSS Mapping**: Base spacing unit multipliers (0.5x, 1x, 1.5x, 2x)

#### `layoutDensity`
- **Type**: `enum`
- **Values**:
  - `compact` - High information density
  - `balanced` - Standard spacing
  - `spacious` - Generous whitespace
  - `minimal` - Maximum whitespace

#### `gridSystem`
- **Type**: `enum`
- **Values**:
  - `standard-12` - 12-column grid
  - `flexible` - CSS Grid with auto-fit
  - `masonry` - Pinterest-style layout
  - `asymmetric` - Broken grid design
  - `modular` - Component-based grid
  - `fluid` - Percentage-based flexible

#### `containerWidth`
- **Type**: `enum`
- **Values**: `narrow` (768px), `standard` (1024px), `wide` (1280px), `full` (100%)

### 1.8 Visual Effects

#### `glassEffect`
- **Type**: `object`
- **Properties**:
  - `blur`: `none`, `subtle`, `medium`, `strong`
  - `opacity`: `10%`, `20%`, `30%`, `40%`
  - `border`: `none`, `subtle`, `visible`
  - `backdrop`: `blur`, `saturate`, `contrast`

#### `glowEffect`
- **Type**: `object`
- **Properties**:
  - `intensity`: `subtle`, `medium`, `strong`, `dramatic`
  - `color`: `brand`, `accent`, `white`, `custom`
  - `spread`: `tight`, `medium`, `wide`
  - `animation`: `static`, `pulse`, `breathing`

#### `shadowComplexity`
- **Type**: `enum`
- **Values**:
  - `simple` - Single shadow layer
  - `layered` - Multiple shadow layers
  - `realistic` - Complex, realistic shadows
  - `artistic` - Creative shadow effects
  - `colored` - Brand-colored shadows
  - `dynamic` - Animated shadows

## 2. Brand-to-Design Mapping

### Industry-Based Parameter Selection

#### Technology/SaaS
- `backgroundType`: `gradient-linear`, `mesh-gradient`, `solid`
- `cardStyle`: `glass`, `elevated`, `flat`
- `buttonStyle`: `gradient`, `solid`, `glass`
- `animationSpeed`: `fast`, `normal`
- `colorStrategy`: `complementary`, `neutral-accent`
- `typographyScale`: `standard`, `generous`

#### Luxury/Premium
- `backgroundType`: `solid`, `gradient-linear`, `texture-subtle`
- `cardStyle`: `elevated`, `floating`, `outlined`
- `buttonStyle`: `outline`, `solid`, `floating`
- `animationSpeed`: `slow`, `slower`
- `colorStrategy`: `monochromatic`, `analogous`
- `typographyScale`: `generous`, `dramatic`

#### Sports/Fitness
- `backgroundType`: `gradient-linear`, `pattern-geometric`, `image-hero`
- `cardStyle`: `floating`, `elevated`, `gradient-border`
- `buttonStyle`: `solid`, `gradient`, `pill`
- `animationSpeed`: `fast`, `normal`
- `colorStrategy`: `high-contrast`, `complementary`
- `typographyScale`: `standard`, `compact`

#### Organic/Natural
- `backgroundType`: `texture-paper`, `pattern-organic`, `gradient-radial`
- `cardStyle`: `organic`, `paper`, `outlined`
- `buttonStyle`: `pill`, `soft`, `organic`
- `animationSpeed`: `slow`, `normal`
- `colorStrategy`: `analogous`, `neutral-accent`
- `typographyScale`: `generous`, `loose`

### Target Audience Mapping

#### Young Adults (18-30)
- Higher animation speeds
- Bold color strategies
- Playful interaction types
- Modern font pairings
- Gradient-heavy designs

#### Professionals (30-50)
- Balanced animation speeds
- Sophisticated color strategies
- Standard interaction types
- Classic font pairings
- Clean, minimal designs

#### Seniors (50+)
- Slower animation speeds
- High contrast colors
- Minimal interaction types
- Readable font pairings
- Simple, clear layouts

## 3. Technical Specifications

### CSS Property Mappings

#### Background Styling
```css
/* Gradient Linear */
background: linear-gradient(135deg, var(--primary), var(--secondary));

/* Glass Morphism */
background: rgba(255, 255, 255, 0.1);
backdrop-filter: blur(10px);
border: 1px solid rgba(255, 255, 255, 0.2);

/* Mesh Gradient */
background: radial-gradient(circle at 20% 80%, var(--accent) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, var(--primary) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, var(--secondary) 0%, transparent 50%);
```

#### Card Styling
```css
/* Elevated Card */
box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
border-radius: var(--card-radius);

/* Neumorphism */
background: var(--surface);
box-shadow: 8px 8px 16px var(--shadow-dark), -8px -8px 16px var(--shadow-light);
```

#### Animation Specifications
```css
/* Standard Transitions */
transition: all var(--animation-speed) var(--animation-easing);

/* Hover Effects */
.button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Micro-interactions */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}
```

### Responsive Behavior Guidelines

#### Breakpoint System
- `xs`: 0px - 475px
- `sm`: 476px - 640px  
- `md`: 641px - 768px
- `lg`: 769px - 1024px
- `xl`: 1025px - 1280px
- `2xl`: 1281px+

#### Responsive Adaptations
- Reduce animation complexity on mobile
- Simplify visual effects for performance
- Adjust spacing scales for smaller screens
- Modify typography scales for readability

### Performance Considerations

#### Optimization Rules
- Limit simultaneous animations to 3-5 elements
- Use `transform` and `opacity` for animations
- Implement `will-change` for animated elements
- Lazy load complex visual effects
- Provide reduced motion alternatives

#### Resource Management
- Compress background images
- Use CSS gradients over image gradients
- Implement efficient shadow techniques
- Optimize font loading strategies

### Accessibility Requirements

#### Color Contrast
- Minimum 4.5:1 ratio for normal text
- Minimum 3:1 ratio for large text
- Provide high contrast mode option

#### Motion Sensitivity
- Respect `prefers-reduced-motion`
- Provide animation disable option
- Use subtle animations by default

#### Focus Management
- Clear focus indicators
- Logical tab order
- Skip navigation options

## 4. Implementation Strategy

### Parameter Consumption by Website AI Agent

#### Input Structure
```json
{
  "brandingData": {
    "industry": "technology",
    "targetAudience": "professionals",
    "brandPersonality": ["innovative", "trustworthy", "modern"],
    "colorPalette": {
      "primary": "#3B82F6",
      "secondary": "#1E40AF", 
      "accent": "#F59E0B"
    }
  },
  "designPreferences": {
    "style": "modern",
    "complexity": "balanced",
    "uniqueness": 0.7
  }
}
```

#### Decision Tree Logic
1. **Industry Analysis** → Base parameter selection
2. **Target Audience** → Refinement of parameters  
3. **Brand Personality** → Style adjustments
4. **Color Palette** → Color strategy selection
5. **Randomness Factor** → Variation injection

#### Validation Rules
- Ensure accessibility compliance
- Validate color contrast ratios
- Check performance impact scores
- Verify responsive behavior

#### Fallback Options
- Default to conservative parameters on errors
- Provide graceful degradation for unsupported effects
- Maintain brand color integrity in all scenarios

### Integration with Branding Platform

#### Data Flow
1. Branding Agent generates brand parameters
2. Website AI Agent receives branding data
3. Parameter mapping engine processes inputs
4. Visual design parameters are generated
5. CSS/styling code is produced
6. Website components are rendered

#### Quality Assurance
- A/B testing framework for parameter combinations
- User feedback integration
- Performance monitoring
- Accessibility auditing

This comprehensive parameter system enables the Website AI Agent to create visually cohesive, brand-appropriate websites that resonate with target audiences while maintaining technical excellence and accessibility standards.
