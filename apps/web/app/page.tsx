'use client'

import { HeroSection } from '../components/sections/HeroSection'
import { AboutSection } from '../components/sections/AboutSection'
import { TestimonialsSection } from '../components/sections/TestimonialsSection'
import { ProductsSection } from '../components/sections/ProductsSection'
import { ControlPanel } from '../components/ControlPanel'
import { useDynamicStyles } from '../hooks/use-dynamic-styles'

export default function Home() {
  // Apply dynamic styles based on design parameters
  useDynamicStyles()

  return (
    <div className="min-h-screen">
      {/* Control Panel */}
      <ControlPanel />

      {/* Website Sections */}
      <HeroSection />
      <AboutSection />
      <TestimonialsSection />
      <ProductsSection />

      {/* Footer */}
      <footer className="bg-[var(--color-foreground)] text-[var(--color-background)] py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold mb-4" style={{ fontFamily: 'var(--font-heading), sans-serif' }}>
                Dynamic Website Builder
              </h3>
              <p className="text-[var(--color-background)]/80 leading-relaxed" style={{ fontFamily: 'var(--font-body), sans-serif' }}>
                Create stunning, responsive websites with our powerful design system.
                Customize every aspect in real-time and see your vision come to life.
              </p>
            </div>

            <div>
              <h4 className="font-semibold mb-4" style={{ fontFamily: 'var(--font-heading), sans-serif' }}>
                Product
              </h4>
              <ul className="space-y-2 text-[var(--color-background)]/80" style={{ fontFamily: 'var(--font-body), sans-serif' }}>
                <li><a href="#" className="hover:text-[var(--color-accent)] transition-colors">Features</a></li>
                <li><a href="#" className="hover:text-[var(--color-accent)] transition-colors">Pricing</a></li>
                <li><a href="#" className="hover:text-[var(--color-accent)] transition-colors">Templates</a></li>
                <li><a href="#" className="hover:text-[var(--color-accent)] transition-colors">Documentation</a></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4" style={{ fontFamily: 'var(--font-heading), sans-serif' }}>
                Company
              </h4>
              <ul className="space-y-2 text-[var(--color-background)]/80" style={{ fontFamily: 'var(--font-body), sans-serif' }}>
                <li><a href="#" className="hover:text-[var(--color-accent)] transition-colors">About</a></li>
                <li><a href="#" className="hover:text-[var(--color-accent)] transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-[var(--color-accent)] transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-[var(--color-accent)] transition-colors">Contact</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-[var(--color-background)]/20 mt-8 pt-8 text-center">
            <p className="text-[var(--color-background)]/60" style={{ fontFamily: 'var(--font-body), sans-serif' }}>
              © 2024 Dynamic Website Builder. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
