'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { DesignParameters, defaultDesignParameters } from '../types/design-parameters'

interface DesignStore {
  parameters: DesignParameters
  updateParameter: <K extends keyof DesignParameters>(
    key: K,
    value: DesignParameters[K]
  ) => void
  updateNestedParameter: <K extends keyof DesignParameters, NK extends keyof DesignParameters[K]>(
    key: K,
    nestedKey: NK,
    value: DesignParameters[K][NK]
  ) => void
  resetParameters: () => void
  loadPreset: (preset: DesignParameters) => void
  exportParameters: () => string
  importParameters: (jsonString: string) => void
}

export const useDesignStore = create<DesignStore>()(
  persist(
    (set, get) => ({
      parameters: defaultDesignParameters,
      
      updateParameter: (key, value) => {
        set((state) => ({
          parameters: {
            ...state.parameters,
            [key]: value,
          },
        }))
      },
      
      updateNestedParameter: (key, nestedKey, value) => {
        set((state) => ({
          parameters: {
            ...state.parameters,
            [key]: {
              ...state.parameters[key],
              [nestedKey]: value,
            },
          },
        }))
      },
      
      resetParameters: () => {
        set({ parameters: defaultDesignParameters })
      },
      
      loadPreset: (preset) => {
        set({ parameters: preset })
      },
      
      exportParameters: () => {
        return JSON.stringify(get().parameters, null, 2)
      },
      
      importParameters: (jsonString) => {
        try {
          const parameters = JSON.parse(jsonString)
          set({ parameters: { ...defaultDesignParameters, ...parameters } })
        } catch (error) {
          console.error('Failed to import parameters:', error)
        }
      },
    }),
    {
      name: 'design-parameters-storage',
      version: 1,
    }
  )
)
