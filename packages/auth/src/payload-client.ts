import { <PERSON>r, <PERSON>, <PERSON>As<PERSON>, LoginResponse, RefreshTokenResponse, APIResponse } from './types.js';

// PayloadCMS API client configuration
export class PayloadClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = 'http://localhost:3002') {
    this.baseURL = baseURL;
    this.loadTokenFromStorage();
  }

  private loadTokenFromStorage() {
    // For cookie-based auth, we don't store the actual token client-side
    // Instead, we check if the user is authenticated via a flag
    if (typeof window !== 'undefined') {
      const isAuthenticated = localStorage.getItem('payload-auth-state');
      // Token will be sent automatically via httpOnly cookie
      this.token = isAuthenticated === 'true' ? 'cookie-based' : null;
    }
  }

  private saveTokenToStorage(_token: string) {
    if (typeof window !== 'undefined') {
      // Store only authentication state, not the actual token
      localStorage.setItem('payload-auth-state', 'true');
    }
    // Set a placeholder to indicate we're authenticated
    this.token = 'cookie-based';
  }

  private removeTokenFromStorage() {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('payload-auth-state');
    }
    this.token = null;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}/api${endpoint}`;
    console.log('PayloadClient: Making request to:', url);

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    // For cookie-based auth, we don't need to manually set Authorization header
    // The httpOnly cookie will be automatically included by the browser
    // We include credentials to ensure cookies are sent with cross-origin requests
    const response = await fetch(url, {
      ...options,
      headers,
      credentials: 'include', // This ensures cookies are sent with the request
    });

    console.log('PayloadClient: Response status:', response.status);
    console.log('PayloadClient: Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      let errorData: any = {};
      try {
        errorData = await response.json();
        console.log('PayloadClient: Error response data:', errorData);
      } catch (parseError) {
        console.log('PayloadClient: Could not parse error response as JSON');
        errorData = {};
      }

      const errorMessage = errorData.message || errorData.error || `HTTP error! status: ${response.status}`;
      console.error('PayloadClient: Request failed with error:', errorMessage);
      throw new Error(errorMessage);
    }

    return response.json();
  }

  // Authentication methods
  async login(email: string, password: string): Promise<LoginResponse> {
    console.log('PayloadClient: Attempting login for:', email);
    console.log('PayloadClient: Base URL:', this.baseURL);
    console.log('PayloadClient: Request payload:', { email, password: '***' });

    try {
      const result = await this.request<LoginResponse>('/users/login', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      });

      console.log('PayloadClient: Login response:', result);

      // With cookie-based auth, the server should set an httpOnly cookie
      // We just need to update our local authentication state
      this.saveTokenToStorage('cookie-based');
      return result;
    } catch (error) {
      console.error('PayloadClient: Login request failed:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await this.request('/users/logout', { method: 'POST' });
    } finally {
      this.removeTokenFromStorage();
    }
  }

  async getCurrentUser(): Promise<User | null> {
    if (!this.token) return null;

    try {
      const result = await this.request<{ user: User }>('/users/me');
      return result.user;
    } catch (error) {
      console.error('Failed to get current user:', error);
      this.removeTokenFromStorage();
      return null;
    }
  }

  async refreshToken(): Promise<RefreshTokenResponse | null> {
    if (!this.token) return null;

    try {
      const result = await this.request<RefreshTokenResponse>('/users/refresh-token', {
        method: 'POST',
      });

      this.saveTokenToStorage(result.token);
      return result;
    } catch (error) {
      console.error('Failed to refresh token:', error);
      this.removeTokenFromStorage();
      return null;
    }
  }

  // Brand methods
  async getBrands(userId?: number): Promise<APIResponse<Brand>> {
    const params = new URLSearchParams();
    if (userId) {
      params.append('where[id][in]', userId.toString());
    }
    
    return this.request<APIResponse<Brand>>(`/brands?${params.toString()}`);
  }

  async getBrandsByUser(userId: number): Promise<APIResponse<Brand>> {
    // Get user with populated brands
    const user = await this.request<User>(`/users/${userId}?depth=1`);
    const brandIds = Array.isArray(user.brands) 
      ? user.brands.map(brand => typeof brand === 'object' ? brand.id : brand)
      : [];

    if (brandIds.length === 0) {
      return { docs: [] };
    }

    const params = new URLSearchParams();
    params.append('where[id][in]', brandIds.join(','));
    
    return this.request<APIResponse<Brand>>(`/brands?${params.toString()}`);
  }

  async createBrand(brandData: Partial<Brand>): Promise<Brand> {
    return this.request<Brand>('/brands', {
      method: 'POST',
      body: JSON.stringify(brandData),
    });
  }

  async updateBrand(id: number, brandData: Partial<Brand>): Promise<Brand> {
    return this.request<Brand>(`/brands/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(brandData),
    });
  }

  async getBrand(id: number): Promise<Brand> {
    return this.request<Brand>(`/brands/${id}`);
  }

  // Brand Asset methods
  async getBrandAssets(brandId?: number): Promise<APIResponse<BrandAsset>> {
    if (!brandId) {
      return this.request<APIResponse<BrandAsset>>('/brandAssets');
    }

    // Get brand with populated brand assets
    const brand = await this.request<Brand>(`/brands/${brandId}?depth=1`);
    const assetIds = Array.isArray(brand.brandAssets)
      ? brand.brandAssets.map(asset => typeof asset === 'object' ? asset.id : asset)
      : [];

    if (assetIds.length === 0) {
      return { docs: [] };
    }

    const params = new URLSearchParams();
    params.append('where[id][in]', assetIds.join(','));
    
    return this.request<APIResponse<BrandAsset>>(`/brandAssets?${params.toString()}`);
  }

  async getBrandAsset(id: number): Promise<BrandAsset> {
    return this.request<BrandAsset>(`/brandAssets/${id}`);
  }

  async createBrandAsset(assetData: Partial<BrandAsset>): Promise<{ doc: BrandAsset }> {
    return this.request<{ doc: BrandAsset }>('/brandAssets', {
      method: 'POST',
      body: JSON.stringify(assetData),
    });
  }

  async updateBrandAsset(id: number, assetData: Partial<BrandAsset>): Promise<BrandAsset> {
    return this.request<BrandAsset>(`/brandAssets/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(assetData),
    });
  }

  async deleteBrandAsset(id: number): Promise<void> {
    await this.request(`/brandAssets/${id}`, {
      method: 'DELETE',
    });
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.token;
  }

  getToken(): string | null {
    // With cookie-based auth, we don't expose the actual token
    // This method now indicates authentication state rather than returning the token
    return this.token;
  }
}

// Create a singleton instance
export const payloadClient = new PayloadClient();
