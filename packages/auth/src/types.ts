// Import and re-export Payload CMS types from shared types package
import type { <PERSON>r, <PERSON>, BrandAsset } from '@repo/types';
export type { Use<PERSON>, <PERSON>, BrandAsset } from '@repo/types';

// Authentication state interface
export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

// Authentication context interface
export interface AuthContextType {
  login: (email: string, password: string) => Promise<any>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<any>;
  checkAuthStatus: () => Promise<void>;
  clearError: () => void;
}

// API state interface
export interface APIState {
  isLoading: boolean;
  error: string | null;
}

// Login response interface
export interface LoginResponse {
  user: User;
  token: string;
}

// Refresh token response interface
export interface RefreshTokenResponse {
  user: User;
  token: string;
}

// API response wrapper
export interface APIResponse<T> {
  docs?: T[];
  data?: T;
  message?: string;
  errors?: Array<{
    message: string;
    field?: string;
  }>;
}
